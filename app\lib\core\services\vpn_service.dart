import 'dart:async';
import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/vpn_connection.dart';
import '../models/server_model.dart';
import '../models/network_stats.dart';
import 'network_service.dart';
import 'server_service.dart';
import 'platform_vpn_service.dart';
import 'security_service.dart';
import 'chain_service.dart';

/// VPN connection management service
class VpnService {
  static final VpnService _instance = VpnService._internal();
  factory VpnService() => _instance;
  VpnService._internal();

  final NetworkService _networkService = NetworkService();
  final ServerService _serverService = ServerService();
  final PlatformVPNService _platformVPN = PlatformVPNService();
  final SecurityService _securityService = SecurityService();
  final ChainService _chainService = ChainService();

  VpnConnection _currentConnection = VpnConnection.disconnected();
  ConnectionStats _connectionStats = ConnectionStats.empty();
  Timer? _connectionTimer;
  Timer? _statsTimer;

  final StreamController<VpnConnection> _connectionController = StreamController<VpnConnection>.broadcast();
  final StreamController<ConnectionStats> _statsController = StreamController<ConnectionStats>.broadcast();

  Stream<VpnConnection> get connectionStream => _connectionController.stream;
  Stream<ConnectionStats> get statsStream => _statsController.stream;
  VpnConnection get currentConnection => _currentConnection;
  ConnectionStats get connectionStats => _connectionStats;

  /// Initialize VPN service
  Future<void> initialize() async {
    await _networkService.initialize();
    await _serverService.initialize();
    await _loadConnectionState();
  }

  /// Connect to VPN server
  Future<bool> connect(String serverId, {VpnProtocol protocol = VpnProtocol.wireguard}) async {
    try {
      final server = _serverService.getServerById(serverId);
      if (server == null) {
        _updateConnection(VpnConnection.error(errorMessage: 'Server not found'));
        return false;
      }

      if (!server.isAvailable) {
        _updateConnection(VpnConnection.error(errorMessage: 'Server is not available'));
        return false;
      }

      // Update to connecting state
      _updateConnection(VpnConnection.connecting(
        serverId: serverId,
        serverName: server.name,
        protocol: protocol,
      ));

      // Simulate connection process (in real implementation, this would use platform channels)
      await _establishConnection(server, protocol);

      return _currentConnection.isConnected;
    } catch (e) {
      _updateConnection(VpnConnection.error(errorMessage: 'Connection failed: $e'));
      return false;
    }
  }

  /// Connect to multiple servers in sequence (multi-hop)
  Future<bool> connectMultiHop(List<String> serverIds, {VpnProtocol protocol = VpnProtocol.wireguard}) async {
    try {
      if (serverIds.isEmpty) {
        _updateConnection(VpnConnection.error(errorMessage: 'No servers specified for multi-hop'));
        return false;
      }

      // Validate all servers
      final servers = <VpnServer>[];
      for (final serverId in serverIds) {
        final server = _serverService.getServerById(serverId);
        if (server == null || !server.isAvailable) {
          _updateConnection(VpnConnection.error(errorMessage: 'One or more servers unavailable'));
          return false;
        }
        servers.add(server);
      }

      // Connect to first server
      final firstServer = servers.first;
      _updateConnection(VpnConnection.connecting(
        serverId: firstServer.id,
        serverName: 'Multi-hop: ${firstServer.name}',
        protocol: protocol,
      ));

      // Simulate multi-hop connection
      await _establishMultiHopConnection(servers, protocol);

      return _currentConnection.isConnected;
    } catch (e) {
      _updateConnection(VpnConnection.error(errorMessage: 'Multi-hop connection failed: $e'));
      return false;
    }
  }

  /// Disconnect from VPN
  Future<void> disconnect() async {
    try {
      if (_currentConnection.isDisconnected) return;

      _updateConnection(_currentConnection.copyWith(status: VpnStatus.disconnecting));

      // Disconnect using platform-specific service
      await _platformVPN.disconnect();

      // Disconnect from any active chain
      await _chainService.disconnectChain();

      // Disable security features
      await _disableSecurityFeatures();

      _updateConnection(VpnConnection.disconnected());
      _stopConnectionMonitoring();
      await _saveConnectionState();
    } catch (e) {
      debugPrint('VpnService: Disconnection failed: $e');
      _updateConnection(VpnConnection.error(errorMessage: 'Disconnection failed: $e'));
    }
  }

  /// Establish actual VPN connection using platform-specific implementation
  Future<void> _establishConnection(VpnServer server, VpnProtocol protocol) async {
    try {
      // Use platform-specific VPN service for actual connection
      final success = await _platformVPN.connectToServer(server, protocol);

      if (!success) {
        _updateConnection(VpnConnection.error(
          errorMessage: 'Failed to establish VPN connection to ${server.name}',
        ));
        return;
      }

      // Check if connection was cancelled during establishment
      if (_currentConnection.status != VpnStatus.connecting) {
        await _platformVPN.disconnect();
        return;
      }

      // Get actual connection details
      final publicIp = await _getPublicIp();
      final vpnIp = await _platformVPN.getVPNIP();

      _updateConnection(VpnConnection.connected(
        serverId: server.id,
        serverName: server.name,
        serverLocation: server.location,
        publicIp: publicIp ?? 'Unknown',
        assignedIp: vpnIp ?? '********',
        protocol: protocol,
        connectedAt: DateTime.now(),
      ));

      // Enable security features
      await _enableSecurityFeatures();

      _startConnectionMonitoring();
      await _saveConnectionState();

    } catch (e) {
      debugPrint('VpnService: Connection establishment failed: $e');
      _updateConnection(VpnConnection.error(
        errorMessage: 'Connection failed: $e',
      ));
    }
  }

  /// Establish actual multi-hop VPN connection using chain service
  Future<void> _establishMultiHopConnection(List<VpnServer> servers, VpnProtocol protocol) async {
    try {
      // Create a temporary chain for this connection
      final chain = _chainService.createChain(
        name: 'Temporary Multi-hop Chain',
        servers: servers,
        description: 'Auto-generated chain for multi-hop connection',
      );

      // Connect to the chain
      final success = await _chainService.connectToChain(chain);

      if (!success) {
        _updateConnection(VpnConnection.error(
          errorMessage: 'Failed to establish multi-hop connection',
        ));
        return;
      }

      // Check if connection was cancelled during establishment
      if (_currentConnection.status != VpnStatus.connecting) {
        await _chainService.disconnectChain();
        return;
      }

      // Get connection details from the chain
      final lastServer = servers.last;
      final publicIp = await _getPublicIp();
      final vpnIp = await _platformVPN.getVPNIP();
      final hopChain = servers.map((s) => s.name).toList();

      _updateConnection(VpnConnection.connected(
        serverId: lastServer.id,
        serverName: 'Multi-hop: ${servers.length} hops',
        serverLocation: lastServer.location,
        publicIp: publicIp ?? 'Unknown',
        assignedIp: vpnIp ?? '********',
        protocol: protocol,
        connectedAt: DateTime.now(),
        hopChain: hopChain,
      ));

      // Enable security features
      await _enableSecurityFeatures();

      _startConnectionMonitoring();
      await _saveConnectionState();

    } catch (e) {
      debugPrint('VpnService: Multi-hop connection failed: $e');
      _updateConnection(VpnConnection.error(
        errorMessage: 'Multi-hop connection failed: $e',
      ));
    }
  }

  /// Get actual public IP address
  Future<String?> _getPublicIp() async {
    try {
      // Use network service to get real public IP
      return await _networkService.getPublicIP();
    } catch (e) {
      debugPrint('VpnService: Failed to get public IP: $e');
      return null;
    }
  }

  /// Enable security features after VPN connection
  Future<void> _enableSecurityFeatures() async {
    try {
      // Enable kill switch
      await _securityService.enableKillSwitch();

      // Enable DNS protection with secure DNS servers
      await _securityService.enableDNSProtection(['*******', '*******']);

      // Enable IPv6 protection
      await _securityService.enableIPv6Protection();

      debugPrint('VpnService: Security features enabled');
    } catch (e) {
      debugPrint('VpnService: Failed to enable security features: $e');
    }
  }

  /// Disable security features after VPN disconnection
  Future<void> _disableSecurityFeatures() async {
    try {
      // Disable kill switch
      await _securityService.disableKillSwitch();

      // Disable DNS protection
      await _securityService.disableDNSProtection();

      // Disable IPv6 protection
      await _securityService.disableIPv6Protection();

      debugPrint('VpnService: Security features disabled');
    } catch (e) {
      debugPrint('VpnService: Failed to disable security features: $e');
    }
  }

  /// Start connection monitoring
  void _startConnectionMonitoring() {
    _connectionTimer?.cancel();
    _statsTimer?.cancel();

    // Update connection duration
    _connectionTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      if (_currentConnection.isConnected && _currentConnection.connectedAt != null) {
        final duration = DateTime.now().difference(_currentConnection.connectedAt!);
        _updateConnection(_currentConnection.copyWith(connectionDuration: duration));
      }
    });

    // Update connection statistics
    _statsTimer = Timer.periodic(const Duration(seconds: 10), (_) async {
      await _updateConnectionStats();
    });
  }

  /// Stop connection monitoring
  void _stopConnectionMonitoring() {
    _connectionTimer?.cancel();
    _statsTimer?.cancel();
    _connectionStats = ConnectionStats.empty();
    _statsController.add(_connectionStats);
  }

  /// Update connection statistics
  Future<void> _updateConnectionStats() async {
    if (!_currentConnection.isConnected) return;

    try {
      final networkStats = await _networkService.measureNetworkPerformance();
      final random = Random();
      
      // Simulate data transfer
      final newBytesReceived = _connectionStats.bytesReceived + random.nextInt(1024 * 1024);
      final newBytesSent = _connectionStats.bytesSent + random.nextInt(512 * 1024);
      
      final updatedHistory = List<NetworkStats>.from(_connectionStats.speedHistory);
      updatedHistory.add(networkStats);
      
      // Keep only last 60 measurements (10 minutes)
      if (updatedHistory.length > 60) {
        updatedHistory.removeAt(0);
      }

      _connectionStats = _connectionStats.copyWith(
        bytesReceived: newBytesReceived,
        bytesSent: newBytesSent,
        connectionTime: _currentConnection.connectionDuration ?? Duration.zero,
        speedHistory: updatedHistory,
      );

      _statsController.add(_connectionStats);
    } catch (e) {
      print('Connection stats update error: $e');
    }
  }

  /// Update connection state
  void _updateConnection(VpnConnection connection) {
    _currentConnection = connection;
    _connectionController.add(_currentConnection);
  }

  /// Save connection state
  Future<void> _saveConnectionState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('vpn_status', _currentConnection.status.name);
      if (_currentConnection.serverId != null) {
        await prefs.setString('last_server_id', _currentConnection.serverId!);
      }
    } catch (e) {
      print('Connection state save error: $e');
    }
  }

  /// Load connection state
  Future<void> _loadConnectionState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedStatus = prefs.getString('vpn_status');
      
      // Only restore if we were connected (for auto-reconnect feature)
      if (savedStatus == VpnStatus.connected.name) {
        final lastServerId = prefs.getString('last_server_id');
        if (lastServerId != null) {
          // Auto-reconnect could be implemented here
          print('Previous connection detected to server: $lastServerId');
        }
      }
    } catch (e) {
      print('Connection state load error: $e');
    }
  }

  /// Get server service (public accessor)
  ServerService get serverService => _serverService;

  /// Dispose resources
  void dispose() {
    _connectionTimer?.cancel();
    _statsTimer?.cancel();
    _connectionController.close();
    _statsController.close();
    _networkService.dispose();
    _serverService.dispose();
  }
}
