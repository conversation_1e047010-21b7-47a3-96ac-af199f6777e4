import 'package:flutter/material.dart';

/// Color palette for Chimera VPN application
/// Features black and red theme with cyberpunk aesthetics
class AppColors {
  // Primary Colors - Red Variants
  static const Color primaryRed = Color(0xFFE53E3E);
  static const Color primaryRedLight = Color(0xFFFC8181);
  static const Color primaryRedDark = Color(0xFFC53030);
  static const Color primaryRedAccent = Color(0xFFFF6B6B);
  
  // Secondary Colors - Black/Gray Variants
  static const Color primaryBlack = Color(0xFF000000);
  static const Color darkGray = Color(0xFF1A1A1A);
  static const Color mediumGray = Color(0xFF2D2D2D);
  static const Color lightGray = Color(0xFF4A4A4A);
  static const Color surfaceGray = Color(0xFF121212);
  
  // Status Colors
  static const Color successGreen = Color(0xFF38A169);
  static const Color warningOrange = Color(0xFFED8936);
  static const Color errorRed = Color(0xFFE53E3E);
  static const Color infoBlue = Color(0xFF3182CE);
  
  // Connection Status Colors
  static const Color connectedGreen = Color(0xFF48BB78);
  static const Color connectingYellow = Color(0xFFECC94B);
  static const Color disconnectedRed = Color(0xFFE53E3E);
  static const Color unstableOrange = Color(0xFFED8936);
  
  // Trust Level Colors
  static const Color trustedGreen = Color(0xFF38A169);
  static const Color communityBlue = Color(0xFF3182CE);
  static const Color untrustedOrange = Color(0xFFED8936);
  static const Color flaggedRed = Color(0xFFE53E3E);
  
  // Text Colors
  static const Color textPrimary = Color(0xFFFFFFFF);
  static const Color textSecondary = Color(0xFFB3B3B3);
  static const Color textTertiary = Color(0xFF666666);
  static const Color textOnRed = Color(0xFFFFFFFF);
  
  // Background Colors
  static const Color backgroundPrimary = Color(0xFF000000);
  static const Color backgroundSecondary = Color(0xFF1A1A1A);
  static const Color backgroundTertiary = Color(0xFF2D2D2D);
  static const Color backgroundCard = Color(0xFF1E1E1E);
  
  // Border Colors
  static const Color borderPrimary = Color(0xFF333333);
  static const Color borderSecondary = Color(0xFF4A4A4A);
  static const Color borderAccent = Color(0xFFE53E3E);
  
  // Gradient Colors
  static const LinearGradient redGradient = LinearGradient(
    colors: [primaryRed, primaryRedDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient blackGradient = LinearGradient(
    colors: [darkGray, primaryBlack],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient cardGradient = LinearGradient(
    colors: [backgroundCard, backgroundSecondary],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Cyberpunk Accent Colors
  static const Color neonRed = Color(0xFFFF073A);
  static const Color neonPink = Color(0xFFFF1744);
  static const Color electricBlue = Color(0xFF00E5FF);
  static const Color cyberGreen = Color(0xFF00FF41);
  
  // Opacity Variants
  static Color redWithOpacity(double opacity) => primaryRed.withOpacity(opacity);
  static Color blackWithOpacity(double opacity) => primaryBlack.withOpacity(opacity);
  static Color whiteWithOpacity(double opacity) => textPrimary.withOpacity(opacity);
}

/// Material 3 Color Scheme for Dark Theme
class AppColorScheme {
  static const ColorScheme darkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    primary: AppColors.primaryRed,
    onPrimary: AppColors.textOnRed,
    secondary: AppColors.primaryRedLight,
    onSecondary: AppColors.textPrimary,
    tertiary: AppColors.neonRed,
    onTertiary: AppColors.textPrimary,
    error: AppColors.errorRed,
    onError: AppColors.textPrimary,
    background: AppColors.backgroundPrimary,
    onBackground: AppColors.textPrimary,
    surface: AppColors.backgroundCard,
    onSurface: AppColors.textPrimary,
    surfaceVariant: AppColors.backgroundSecondary,
    onSurfaceVariant: AppColors.textSecondary,
    outline: AppColors.borderPrimary,
    outlineVariant: AppColors.borderSecondary,
    shadow: AppColors.primaryBlack,
    scrim: Color(0x80000000), // Black with 50% opacity
    inverseSurface: AppColors.textPrimary,
    onInverseSurface: AppColors.primaryBlack,
    inversePrimary: AppColors.primaryRedDark,
    surfaceTint: AppColors.primaryRed,
  );
  
  static const ColorScheme lightColorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: AppColors.primaryRedDark,
    onPrimary: AppColors.textPrimary,
    secondary: AppColors.primaryRed,
    onSecondary: AppColors.textPrimary,
    tertiary: AppColors.primaryRedAccent,
    onTertiary: AppColors.textPrimary,
    error: AppColors.errorRed,
    onError: AppColors.textPrimary,
    background: AppColors.lightGray,
    onBackground: AppColors.primaryBlack,
    surface: AppColors.textPrimary,
    onSurface: AppColors.primaryBlack,
    surfaceVariant: AppColors.mediumGray,
    onSurfaceVariant: AppColors.textPrimary,
    outline: AppColors.borderSecondary,
    outlineVariant: AppColors.borderPrimary,
    shadow: AppColors.lightGray,
    scrim: Color(0x80FFFFFF), // White with 50% opacity
    inverseSurface: AppColors.primaryBlack,
    onInverseSurface: AppColors.textPrimary,
    inversePrimary: AppColors.primaryRedLight,
    surfaceTint: AppColors.primaryRedDark,
  );
}
