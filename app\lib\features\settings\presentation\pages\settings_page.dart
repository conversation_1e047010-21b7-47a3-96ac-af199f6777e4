import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/chimera_card.dart';
import '../../../../core/widgets/chimera_button.dart';
import '../../../../core/providers/theme_provider.dart';

/// Settings page for app configuration and preferences
class SettingsPage extends ConsumerStatefulWidget {
  const SettingsPage({super.key});

  @override
  ConsumerState<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends ConsumerState<SettingsPage> {
  bool _killSwitchEnabled = true;
  bool _dnsProtectionEnabled = true;
  bool _ipv6ProtectionEnabled = true;
  bool _autoConnectEnabled = false;
  bool _notificationsEnabled = true;
  String _selectedDnsProvider = 'Cloudflare';
  String _selectedProtocol = 'WireGuard';

  @override
  Widget build(BuildContext context) {
    final themeNotifier = ref.read(themeModeProvider.notifier);
    final currentTheme = ref.watch(themeModeProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Theme Settings
            _buildThemeSection(themeNotifier, currentTheme),
            
            const SizedBox(height: 24),
            
            // Security Settings
            _buildSecuritySection(),
            
            const SizedBox(height: 24),
            
            // Connection Settings
            _buildConnectionSection(),
            
            const SizedBox(height: 24),
            
            // Notification Settings
            _buildNotificationSection(),
            
            const SizedBox(height: 24),
            
            // Advanced Settings
            _buildAdvancedSection(),
            
            const SizedBox(height: 24),
            
            // About Section
            _buildAboutSection(),
            
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildThemeSection(ThemeModeNotifier themeNotifier, ThemeMode currentTheme) {
    return _buildSettingsSection(
      'Appearance',
      Icons.palette,
      [
        _buildSettingsTile(
          'Theme',
          'Choose your preferred theme',
          trailing: DropdownButton<ThemeMode>(
            value: currentTheme,
            underline: const SizedBox(),
            items: const [
              DropdownMenuItem(
                value: ThemeMode.dark,
                child: Text('Dark'),
              ),
              DropdownMenuItem(
                value: ThemeMode.light,
                child: Text('Light'),
              ),
              DropdownMenuItem(
                value: ThemeMode.system,
                child: Text('System'),
              ),
            ],
            onChanged: (ThemeMode? mode) {
              if (mode != null) {
                switch (mode) {
                  case ThemeMode.dark:
                    themeNotifier.setDarkMode();
                    break;
                  case ThemeMode.light:
                    themeNotifier.setLightMode();
                    break;
                  case ThemeMode.system:
                    themeNotifier.setSystemMode();
                    break;
                }
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSecuritySection() {
    return _buildSettingsSection(
      'Security',
      Icons.security,
      [
        _buildSwitchTile(
          'Kill Switch',
          'Block all traffic if VPN disconnects',
          _killSwitchEnabled,
          (value) => setState(() => _killSwitchEnabled = value),
        ),
        _buildSwitchTile(
          'DNS Protection',
          'Prevent DNS leaks',
          _dnsProtectionEnabled,
          (value) => setState(() => _dnsProtectionEnabled = value),
        ),
        _buildSwitchTile(
          'IPv6 Protection',
          'Block IPv6 traffic to prevent leaks',
          _ipv6ProtectionEnabled,
          (value) => setState(() => _ipv6ProtectionEnabled = value),
        ),
        _buildSettingsTile(
          'DNS Provider',
          'Choose your DNS provider',
          trailing: DropdownButton<String>(
            value: _selectedDnsProvider,
            underline: const SizedBox(),
            items: const [
              DropdownMenuItem(value: 'Cloudflare', child: Text('Cloudflare')),
              DropdownMenuItem(value: 'Quad9', child: Text('Quad9')),
              DropdownMenuItem(value: 'OpenDNS', child: Text('OpenDNS')),
              DropdownMenuItem(value: 'Custom', child: Text('Custom')),
            ],
            onChanged: (String? provider) {
              if (provider != null) {
                setState(() => _selectedDnsProvider = provider);
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildConnectionSection() {
    return _buildSettingsSection(
      'Connection',
      Icons.wifi,
      [
        _buildSwitchTile(
          'Auto Connect',
          'Automatically connect on app start',
          _autoConnectEnabled,
          (value) => setState(() => _autoConnectEnabled = value),
        ),
        _buildSettingsTile(
          'Default Protocol',
          'Preferred VPN protocol',
          trailing: DropdownButton<String>(
            value: _selectedProtocol,
            underline: const SizedBox(),
            items: const [
              DropdownMenuItem(value: 'WireGuard', child: Text('WireGuard')),
              DropdownMenuItem(value: 'OpenVPN UDP', child: Text('OpenVPN UDP')),
              DropdownMenuItem(value: 'OpenVPN TCP', child: Text('OpenVPN TCP')),
            ],
            onChanged: (String? protocol) {
              if (protocol != null) {
                setState(() => _selectedProtocol = protocol);
              }
            },
          ),
        ),
        _buildSettingsTile(
          'Connection Timeout',
          'Maximum time to wait for connection - 30 seconds',
          onTap: () {
            // TODO: Show timeout selection dialog
          },
        ),
      ],
    );
  }

  Widget _buildNotificationSection() {
    return _buildSettingsSection(
      'Notifications',
      Icons.notifications,
      [
        _buildSwitchTile(
          'Enable Notifications',
          'Receive connection and security alerts',
          _notificationsEnabled,
          (value) => setState(() => _notificationsEnabled = value),
        ),
        _buildSettingsTile(
          'Connection Alerts',
          'Notify when connection status changes',
          onTap: () {
            // TODO: Configure connection alerts
          },
        ),
        _buildSettingsTile(
          'Security Warnings',
          'Notify about security issues',
          onTap: () {
            // TODO: Configure security warnings
          },
        ),
      ],
    );
  }

  Widget _buildAdvancedSection() {
    return _buildSettingsSection(
      'Advanced',
      Icons.settings_applications,
      [
        _buildSettingsTile(
          'Server Management',
          'Manage trusted and community servers',
          onTap: () {
            // TODO: Navigate to server management
          },
        ),
        _buildSettingsTile(
          'Logs',
          'View connection and debug logs',
          onTap: () {
            // TODO: Navigate to logs
          },
        ),
        _buildSettingsTile(
          'Export Configuration',
          'Export VPN configurations',
          onTap: () {
            // TODO: Export configurations
          },
        ),
        _buildSettingsTile(
          'Reset Settings',
          'Reset all settings to default',
          onTap: _showResetDialog,
        ),
      ],
    );
  }

  Widget _buildAboutSection() {
    return _buildSettingsSection(
      'About',
      Icons.info,
      [
        _buildSettingsTile(
          'Version',
          'Chimera VPN v1.0.0',
        ),
        _buildSettingsTile(
          'Privacy Policy',
          'Read our privacy policy',
          onTap: () {
            // TODO: Show privacy policy
          },
        ),
        _buildSettingsTile(
          'Terms of Service',
          'Read terms of service',
          onTap: () {
            // TODO: Show terms of service
          },
        ),
        _buildSettingsTile(
          'Support',
          'Get help and support',
          onTap: () {
            // TODO: Open support
          },
        ),
      ],
    );
  }

  Widget _buildSettingsSection(String title, IconData icon, List<Widget> children) {
    return ChimeraCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: AppColors.primaryRed, size: 24),
              const SizedBox(width: 8),
              Text(title, style: AppTextStyles.titleMedium),
            ],
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSettingsTile(
    String title,
    String subtitle, {
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return ListTile(
      title: Text(title, style: AppTextStyles.bodyMedium),
      subtitle: Text(subtitle, style: AppTextStyles.bodySmall),
      trailing: trailing ?? (onTap != null ? const Icon(Icons.arrow_forward_ios, size: 16) : null),
      onTap: onTap,
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return ListTile(
      title: Text(title, style: AppTextStyles.bodyMedium),
      subtitle: Text(subtitle, style: AppTextStyles.bodySmall),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
      ),
      contentPadding: EdgeInsets.zero,
    );
  }

  void _showResetDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Settings'),
        content: const Text('Are you sure you want to reset all settings to default? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ChimeraButton(
            text: 'Reset',
            type: ChimeraButtonType.danger,
            onPressed: () {
              Navigator.of(context).pop();
              _resetSettings();
            },
          ),
        ],
      ),
    );
  }

  void _resetSettings() {
    setState(() {
      _killSwitchEnabled = true;
      _dnsProtectionEnabled = true;
      _ipv6ProtectionEnabled = true;
      _autoConnectEnabled = false;
      _notificationsEnabled = true;
      _selectedDnsProvider = 'Cloudflare';
      _selectedProtocol = 'WireGuard';
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Settings reset to default')),
    );
  }
}
