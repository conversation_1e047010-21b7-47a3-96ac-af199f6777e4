name: chimera_vpn
description: A next-generation, highly secure VPN application with multi-hop chain architecture.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.2.0 <4.0.0'
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter
  
  # State Management
  riverpod: ^2.4.0
  flutter_riverpod: ^2.4.0
  
  # UI Components
  cupertino_icons: ^1.0.2
  material_design_icons_flutter: ^7.0.0
  flutter_svg: ^2.0.0
  animations: ^2.0.0
  
  # Networking
  dio: ^5.3.0
  connectivity_plus: ^5.0.0
  
  # Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  flutter_secure_storage: ^9.0.0
  shared_preferences: ^2.2.0
  
  # Platform Integration
  method_channel: ^0.1.0
  ffi: ^2.1.0
  
  # VPN Integration
  network_info_plus: ^4.1.0
  permission_handler: ^11.0.0
  
  # Charts and Visualization
  fl_chart: ^0.65.0
  
  # QR Code
  qr_code_scanner: ^1.0.1
  qr_flutter: ^4.1.0
  
  # Utils
  intl: ^0.18.0
  uuid: ^4.0.0
  path_provider: ^2.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  build_runner: ^2.4.0
  hive_generator: ^2.0.0

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
  
  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Medium.ttf
          weight: 500
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
    - family: RobotoMono
      fonts:
        - asset: assets/fonts/RobotoMono-Regular.ttf
        - asset: assets/fonts/RobotoMono-Bold.ttf
          weight: 700
