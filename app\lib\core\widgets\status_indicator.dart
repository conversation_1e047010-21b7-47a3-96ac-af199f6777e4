import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';

/// Status indicator widget for showing connection and security status
class StatusIndicator extends StatelessWidget {
  final String status;
  final StatusType type;
  final bool showIcon;
  final bool showText;
  final double size;

  const StatusIndicator({
    super.key,
    required this.status,
    required this.type,
    this.showIcon = true,
    this.showText = true,
    this.size = 24,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (showIcon) ...[
          Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              color: _getStatusColor(),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: _getStatusColor().withOpacity(0.3),
                  blurRadius: 4,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: Icon(
              _getStatusIcon(),
              color: AppColors.textPrimary,
              size: size * 0.6,
            ),
          ),
          if (showText) const SizedBox(width: 8),
        ],
        if (showText)
          Text(
            status,
            style: AppTextStyles.getConnectionStatusStyle(status),
          ),
      ],
    );
  }

  Color _getStatusColor() {
    switch (type) {
      case StatusType.connection:
        return _getConnectionStatusColor();
      case StatusType.security:
        return _getSecurityStatusColor();
      case StatusType.trust:
        return _getTrustStatusColor();
      case StatusType.performance:
        return _getPerformanceStatusColor();
    }
  }

  Color _getConnectionStatusColor() {
    switch (status.toLowerCase()) {
      case 'connected':
        return AppColors.connectedGreen;
      case 'connecting':
        return AppColors.connectingYellow;
      case 'disconnected':
        return AppColors.disconnectedRed;
      case 'unstable':
        return AppColors.unstableOrange;
      default:
        return AppColors.textSecondary;
    }
  }

  Color _getSecurityStatusColor() {
    switch (status.toLowerCase()) {
      case 'secure':
      case 'protected':
        return AppColors.successGreen;
      case 'warning':
      case 'vulnerable':
        return AppColors.warningOrange;
      case 'insecure':
      case 'exposed':
        return AppColors.errorRed;
      default:
        return AppColors.textSecondary;
    }
  }

  Color _getTrustStatusColor() {
    switch (status.toLowerCase()) {
      case 'trusted':
        return AppColors.trustedGreen;
      case 'community':
        return AppColors.communityBlue;
      case 'untrusted':
        return AppColors.untrustedOrange;
      case 'flagged':
        return AppColors.flaggedRed;
      default:
        return AppColors.textSecondary;
    }
  }

  Color _getPerformanceStatusColor() {
    switch (status.toLowerCase()) {
      case 'excellent':
      case 'good':
        return AppColors.successGreen;
      case 'fair':
      case 'moderate':
        return AppColors.warningOrange;
      case 'poor':
      case 'slow':
        return AppColors.errorRed;
      default:
        return AppColors.textSecondary;
    }
  }

  IconData _getStatusIcon() {
    switch (type) {
      case StatusType.connection:
        return _getConnectionIcon();
      case StatusType.security:
        return _getSecurityIcon();
      case StatusType.trust:
        return _getTrustIcon();
      case StatusType.performance:
        return _getPerformanceIcon();
    }
  }

  IconData _getConnectionIcon() {
    switch (status.toLowerCase()) {
      case 'connected':
        return Icons.check_circle;
      case 'connecting':
        return Icons.sync;
      case 'disconnected':
        return Icons.cancel;
      case 'unstable':
        return Icons.warning;
      default:
        return Icons.help;
    }
  }

  IconData _getSecurityIcon() {
    switch (status.toLowerCase()) {
      case 'secure':
      case 'protected':
        return Icons.security;
      case 'warning':
      case 'vulnerable':
        return Icons.warning;
      case 'insecure':
      case 'exposed':
        return Icons.dangerous;
      default:
        return Icons.help;
    }
  }

  IconData _getTrustIcon() {
    switch (status.toLowerCase()) {
      case 'trusted':
        return Icons.verified;
      case 'community':
        return Icons.people;
      case 'untrusted':
        return Icons.warning;
      case 'flagged':
        return Icons.flag;
      default:
        return Icons.help;
    }
  }

  IconData _getPerformanceIcon() {
    switch (status.toLowerCase()) {
      case 'excellent':
        return Icons.speed;
      case 'good':
        return Icons.trending_up;
      case 'fair':
      case 'moderate':
        return Icons.trending_flat;
      case 'poor':
      case 'slow':
        return Icons.trending_down;
      default:
        return Icons.help;
    }
  }
}

/// Animated status indicator with pulsing effect
class AnimatedStatusIndicator extends StatefulWidget {
  final String status;
  final StatusType type;
  final bool showIcon;
  final bool showText;
  final double size;

  const AnimatedStatusIndicator({
    super.key,
    required this.status,
    required this.type,
    this.showIcon = true,
    this.showText = true,
    this.size = 24,
  });

  @override
  State<AnimatedStatusIndicator> createState() => _AnimatedStatusIndicatorState();
}

class _AnimatedStatusIndicatorState extends State<AnimatedStatusIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Only animate for connecting status
    if (widget.status.toLowerCase() == 'connecting') {
      _animationController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(AnimatedStatusIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.status.toLowerCase() == 'connecting') {
      _animationController.repeat(reverse: true);
    } else {
      _animationController.stop();
      _animationController.reset();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: widget.status.toLowerCase() == 'connecting' 
              ? _pulseAnimation.value 
              : 1.0,
          child: StatusIndicator(
            status: widget.status,
            type: widget.type,
            showIcon: widget.showIcon,
            showText: widget.showText,
            size: widget.size,
          ),
        );
      },
    );
  }
}

/// Status type enumeration
enum StatusType {
  connection,
  security,
  trust,
  performance,
}
