# Chimera VPN - Flutter Application

A next-generation, highly secure VPN application with multi-hop chain architecture, built with Flutter.

## Features

- **Multi-Hop VPN Chains**: Create custom chains with multiple server hops for enhanced privacy
- **Protocol Flexibility**: Support for WireGuard and OpenVPN protocols
- **Black & Red Theme**: Cyberpunk-inspired dark theme with red accents
- **Cross-Platform**: Runs on iOS, Android, Windows, macOS, and Linux
- **Security-First**: Kill switch, DNS protection, and IPv6 leak prevention
- **Visual Chain Builder**: Drag-and-drop interface for creating VPN chains
- **Performance Monitoring**: Real-time connection metrics and performance data

## Architecture

The app follows a clean architecture pattern with feature-based organization:

```
lib/
├── core/                   # Core functionality
│   ├── theme/             # App theming (colors, text styles)
│   ├── widgets/           # Reusable UI components
│   ├── providers/         # State management providers
│   └── navigation/        # App routing
├── features/              # Feature modules
│   ├── dashboard/         # Main dashboard
│   ├── chain_builder/     # VPN chain creation
│   ├── settings/          # App settings
│   ├── server_manager/    # Server management
│   └── connection/        # Connection details
└── main.dart             # App entry point
```

## Theme System

The app uses a custom black and red theme system:

- **Primary Colors**: Various shades of red (#E53E3E, #FC8181, #C53030)
- **Background Colors**: Black and dark gray variants
- **Status Colors**: Green (connected), Yellow (connecting), Red (disconnected)
- **Trust Colors**: Green (trusted), Blue (community), Orange (untrusted), Red (flagged)

## Getting Started

### Prerequisites

- Flutter SDK 3.16.0 or higher
- Dart SDK 3.2.0 or higher

### Installation

1. Clone the repository
2. Navigate to the app directory
3. Install dependencies:
   ```bash
   flutter pub get
   ```
4. Run the app:
   ```bash
   flutter run
   ```

### Development

The app uses several key packages:

- **State Management**: Riverpod for reactive state management
- **UI Components**: Material 3 with custom theming
- **Storage**: Hive for local data storage
- **Networking**: Dio for HTTP requests
- **Charts**: FL Chart for performance visualization

## Current Status

This is the initial UI/UX implementation with:

✅ **Completed**:
- Project structure setup
- Black and red theme system
- Core UI components (buttons, cards, status indicators)
- Main dashboard with connection status
- Chain builder interface
- Settings page with theme switching
- Navigation system

🚧 **In Progress**:
- Chain builder implementation
- Settings page implementation

📋 **Planned**:
- VPN protocol integration
- Server management
- Performance monitoring
- Security features implementation

## Contributing

1. Follow the established architecture patterns
2. Use the custom theme system for consistent styling
3. Write tests for new features
4. Follow Flutter best practices and linting rules

## License

This project is part of the Chimera VPN suite. See the main project LICENSE file for details.
