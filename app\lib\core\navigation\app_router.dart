import 'package:flutter/material.dart';
import '../../features/dashboard/presentation/pages/dashboard_page.dart';
import '../../features/chain_builder/presentation/pages/chain_builder_page.dart';
import '../../features/settings/presentation/pages/settings_page.dart';
import '../../features/server_manager/presentation/pages/server_manager_page.dart';
import '../../features/connection/presentation/pages/connection_details_page.dart';

/// Application router for managing navigation routes
class AppRouter {
  // Route names
  static const String dashboard = '/dashboard';
  static const String chainBuilder = '/chain-builder';
  static const String settings = '/settings';
  static const String serverManager = '/server-manager';
  static const String connectionDetails = '/connection-details';

  /// Map of route names to widget builders
  static Map<String, WidgetBuilder> get routes {
    return {
      dashboard: (context) => const DashboardPage(),
      chainBuilder: (context) => const ChainBuilderPage(),
      settings: (context) => const SettingsPage(),
      serverManager: (context) => const ServerManagerPage(),
      connectionDetails: (context) => const ConnectionDetailsPage(),
    };
  }

  /// Generate route for dynamic routing
  static Route<dynamic>? generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case AppRouter.dashboard:
        return MaterialPageRoute(
          builder: (context) => const DashboardPage(),
          settings: settings,
        );

      case AppRouter.chainBuilder:
        return MaterialPageRoute(
          builder: (context) => const ChainBuilderPage(),
          settings: settings,
        );

      case AppRouter.settings:
        return MaterialPageRoute(
          builder: (context) => const SettingsPage(),
          settings: settings,
        );

      case AppRouter.serverManager:
        return MaterialPageRoute(
          builder: (context) => const ServerManagerPage(),
          settings: settings,
        );

      case AppRouter.connectionDetails:
        return MaterialPageRoute(
          builder: (context) => const ConnectionDetailsPage(),
          settings: settings,
        );
      
      default:
        return MaterialPageRoute(
          builder: (context) => const DashboardPage(),
          settings: settings,
        );
    }
  }

  /// Navigate to dashboard
  static void toDashboard(BuildContext context) {
    Navigator.of(context).pushNamedAndRemoveUntil(
      dashboard,
      (route) => false,
    );
  }

  /// Navigate to chain builder
  static void toChainBuilder(BuildContext context) {
    Navigator.of(context).pushNamed(chainBuilder);
  }

  /// Navigate to settings
  static void toSettings(BuildContext context) {
    Navigator.of(context).pushNamed(settings);
  }

  /// Navigate to server manager
  static void toServerManager(BuildContext context) {
    Navigator.of(context).pushNamed(serverManager);
  }

  /// Navigate to connection details
  static void toConnectionDetails(BuildContext context) {
    Navigator.of(context).pushNamed(connectionDetails);
  }

  /// Go back to previous screen
  static void goBack(BuildContext context) {
    if (Navigator.of(context).canPop()) {
      Navigator.of(context).pop();
    }
  }

  /// Replace current route with new route
  static void replaceTo(BuildContext context, String routeName) {
    Navigator.of(context).pushReplacementNamed(routeName);
  }

  /// Push and remove all previous routes
  static void pushAndRemoveAll(BuildContext context, String routeName) {
    Navigator.of(context).pushNamedAndRemoveUntil(
      routeName,
      (route) => false,
    );
  }
}
