import 'package:flutter/material.dart';
import 'app_colors.dart';

/// Text styles for Chimera VPN application
/// Following Material 3 typography guidelines with cyberpunk aesthetics
class AppTextStyles {
  // Private constructor to prevent instantiation
  AppTextStyles._();

  // Display Styles
  static const TextStyle displayLarge = TextStyle(
    fontSize: 57,
    fontWeight: FontWeight.w400,
    letterSpacing: -0.25,
    color: AppColors.textPrimary,
    fontFamily: 'Roboto',
  );

  static const TextStyle displayMedium = TextStyle(
    fontSize: 45,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    color: AppColors.textPrimary,
    fontFamily: 'Roboto',
  );

  static const TextStyle displaySmall = TextStyle(
    fontSize: 36,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    color: AppColors.textPrimary,
    fontFamily: 'Roboto',
  );

  // Headline Styles
  static const TextStyle headlineLarge = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    color: AppColors.textPrimary,
    fontFamily: 'Roboto',
  );

  static const TextStyle headlineMedium = TextStyle(
    fontSize: 28,
    fontWeight: FontWeight.w500,
    letterSpacing: 0,
    color: AppColors.textPrimary,
    fontFamily: 'Roboto',
  );

  static const TextStyle headlineSmall = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w500,
    letterSpacing: 0,
    color: AppColors.textPrimary,
    fontFamily: 'Roboto',
  );

  // Title Styles
  static const TextStyle titleLarge = TextStyle(
    fontSize: 22,
    fontWeight: FontWeight.w500,
    letterSpacing: 0,
    color: AppColors.textPrimary,
    fontFamily: 'Roboto',
  );

  static const TextStyle titleMedium = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.15,
    color: AppColors.textPrimary,
    fontFamily: 'Roboto',
  );

  static const TextStyle titleSmall = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.1,
    color: AppColors.textPrimary,
    fontFamily: 'Roboto',
  );

  // Body Styles
  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.5,
    color: AppColors.textPrimary,
    fontFamily: 'Roboto',
  );

  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.25,
    color: AppColors.textPrimary,
    fontFamily: 'Roboto',
  );

  static const TextStyle bodySmall = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.4,
    color: AppColors.textSecondary,
    fontFamily: 'Roboto',
  );

  // Label Styles
  static const TextStyle labelLarge = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.1,
    color: AppColors.textPrimary,
    fontFamily: 'Roboto',
  );

  static const TextStyle labelMedium = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.5,
    color: AppColors.textPrimary,
    fontFamily: 'Roboto',
  );

  static const TextStyle labelSmall = TextStyle(
    fontSize: 11,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.5,
    color: AppColors.textSecondary,
    fontFamily: 'Roboto',
  );

  // Custom Styles for VPN Application
  static const TextStyle connectionStatus = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    letterSpacing: 0.5,
    color: AppColors.connectedGreen,
    fontFamily: 'Roboto',
  );

  static const TextStyle serverName = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.25,
    color: AppColors.textPrimary,
    fontFamily: 'Roboto',
  );

  static const TextStyle latencyText = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.4,
    color: AppColors.textSecondary,
    fontFamily: 'RobotoMono',
  );

  static const TextStyle ipAddress = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.5,
    color: AppColors.textSecondary,
    fontFamily: 'RobotoMono',
  );

  static const TextStyle chainHopNumber = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w700,
    letterSpacing: 0,
    color: AppColors.primaryRed,
    fontFamily: 'Roboto',
  );

  static const TextStyle protocolBadge = TextStyle(
    fontSize: 10,
    fontWeight: FontWeight.w600,
    letterSpacing: 0.5,
    color: AppColors.textOnRed,
    fontFamily: 'Roboto',
  );

  static const TextStyle trustLevel = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.25,
    color: AppColors.textSecondary,
    fontFamily: 'Roboto',
  );

  static const TextStyle dataTransfer = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.25,
    color: AppColors.textPrimary,
    fontFamily: 'RobotoMono',
  );

  static const TextStyle warningText = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.25,
    color: AppColors.warningOrange,
    fontFamily: 'Roboto',
  );

  static const TextStyle errorText = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.25,
    color: AppColors.errorRed,
    fontFamily: 'Roboto',
  );

  static const TextStyle successText = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.25,
    color: AppColors.successGreen,
    fontFamily: 'Roboto',
  );

  // Complete Text Theme for Material 3
  static const TextTheme textTheme = TextTheme(
    displayLarge: displayLarge,
    displayMedium: displayMedium,
    displaySmall: displaySmall,
    headlineLarge: headlineLarge,
    headlineMedium: headlineMedium,
    headlineSmall: headlineSmall,
    titleLarge: titleLarge,
    titleMedium: titleMedium,
    titleSmall: titleSmall,
    bodyLarge: bodyLarge,
    bodyMedium: bodyMedium,
    bodySmall: bodySmall,
    labelLarge: labelLarge,
    labelMedium: labelMedium,
    labelSmall: labelSmall,
  );

  // Utility methods for dynamic text styles
  static TextStyle getConnectionStatusStyle(String status) {
    switch (status.toLowerCase()) {
      case 'connected':
        return connectionStatus.copyWith(color: AppColors.connectedGreen);
      case 'connecting':
        return connectionStatus.copyWith(color: AppColors.connectingYellow);
      case 'disconnected':
        return connectionStatus.copyWith(color: AppColors.disconnectedRed);
      case 'unstable':
        return connectionStatus.copyWith(color: AppColors.unstableOrange);
      default:
        return connectionStatus;
    }
  }

  static TextStyle getTrustLevelStyle(String trustLevel) {
    switch (trustLevel.toLowerCase()) {
      case 'trusted':
        return AppTextStyles.trustLevel.copyWith(color: AppColors.trustedGreen);
      case 'community':
        return AppTextStyles.trustLevel.copyWith(color: AppColors.communityBlue);
      case 'untrusted':
        return AppTextStyles.trustLevel.copyWith(color: AppColors.untrustedOrange);
      case 'flagged':
        return AppTextStyles.trustLevel.copyWith(color: AppColors.flaggedRed);
      default:
        return AppTextStyles.trustLevel;
    }
  }
}
