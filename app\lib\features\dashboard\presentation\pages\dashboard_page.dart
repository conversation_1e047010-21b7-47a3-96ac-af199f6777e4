import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/chimera_card.dart';
import '../../../../core/widgets/chimera_button.dart';
import '../../../../core/widgets/status_indicator.dart';


/// Main dashboard page showing connection status and controls
class DashboardPage extends ConsumerStatefulWidget {
  const DashboardPage({super.key});

  @override
  ConsumerState<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends ConsumerState<DashboardPage> {
  bool _isConnected = false;
  String _connectionStatus = 'Disconnected';
  String _currentChain = 'No active chain';
  String _exitLocation = 'Unknown';
  String _publicIP = '*************';
  double _uploadSpeed = 0.0;
  double _downloadSpeed = 0.0;
  int _totalHops = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Chimera VPN'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshStatus,
          ),
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              // TODO: Show notifications
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _refreshStatus,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Connection Status Section
              _buildConnectionStatusCard(),

              const SizedBox(height: 16),

              // Quick Actions Section
              _buildQuickActionsCard(),
              
              const SizedBox(height: 16),
              
              // Chain Visualization Section
              if (_isConnected && _totalHops > 0)
                _buildChainVisualizationCard(),
              
              const SizedBox(height: 16),
              
              // Performance Metrics Section
              _buildPerformanceMetricsCard(),
              
              const SizedBox(height: 16),
              
              // Security Status Section
              _buildSecurityStatusCard(),
              
              const SizedBox(height: 16),
              
              // Recent Activity Section
              _buildRecentActivityCard(),
              
              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildConnectionStatusCard() {
    return ChimeraCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              AnimatedStatusIndicator(
                status: _connectionStatus,
                type: StatusType.connection,
                size: 32,
                showText: false,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _connectionStatus,
                      style: AppTextStyles.getConnectionStatusStyle(_connectionStatus),
                    ),
                    if (_isConnected) ...[
                      Text(
                        _currentChain,
                        style: AppTextStyles.bodyMedium,
                      ),
                    ],
                  ],
                ),
              ),
              ChimeraButton(
                text: _isConnected ? 'Disconnect' : 'Connect',
                type: _isConnected ? ChimeraButtonType.danger : ChimeraButtonType.primary,
                onPressed: _toggleConnection,
              ),
            ],
          ),
          if (_isConnected) ...[
            const SizedBox(height: 16),
            const Divider(color: AppColors.borderPrimary),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem('Public IP', _publicIP, Icons.public),
                ),
                Expanded(
                  child: _buildInfoItem('Exit Location', _exitLocation, Icons.location_on),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: AppColors.textSecondary),
            const SizedBox(width: 4),
            Text(label, style: AppTextStyles.labelSmall),
          ],
        ),
        const SizedBox(height: 4),
        Text(value, style: AppTextStyles.bodyMedium),
      ],
    );
  }

  Widget _buildQuickActionsCard() {
    return ChimeraCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Actions',
            style: AppTextStyles.titleMedium,
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ChimeraButton(
                  text: 'Build Chain',
                  type: ChimeraButtonType.outline,
                  icon: Icons.account_tree,
                  onPressed: () {
                    // TODO: Navigate to chain builder
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ChimeraButton(
                  text: 'Select Server',
                  type: ChimeraButtonType.outline,
                  icon: Icons.dns,
                  onPressed: () {
                    // TODO: Navigate to server selection
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ChimeraButton(
            text: 'Settings',
            type: ChimeraButtonType.text,
            icon: Icons.settings,
            isFullWidth: true,
            onPressed: () {
              // TODO: Navigate to settings
            },
          ),
        ],
      ),
    );
  }

  Widget _buildChainVisualizationCard() {
    return ChimeraCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.account_tree,
                color: AppColors.primaryRed,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Active Chain',
                style: AppTextStyles.titleMedium,
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildChainVisualization(),
        ],
      ),
    );
  }

  Widget _buildChainVisualization() {
    final hops = _generateMockHops();
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          _buildChainNode('You', Icons.computer, AppColors.textSecondary, true),
          ...hops.asMap().entries.map((entry) {
            final index = entry.key;
            final hop = entry.value;
            return Row(
              children: [
                _buildChainArrow(),
                _buildChainNode(hop, Icons.dns, AppColors.primaryRed, false),
              ],
            );
          }).toList(),
          _buildChainArrow(),
          _buildChainNode('Internet', Icons.public, AppColors.successGreen, true),
        ],
      ),
    );
  }

  Widget _buildChainNode(String label, IconData icon, Color color, bool isEndpoint) {
    return Column(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: color.withOpacity(0.2),
            borderRadius: BorderRadius.circular(24),
            border: Border.all(color: color, width: 2),
          ),
          child: Icon(icon, color: color, size: 24),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: AppTextStyles.labelSmall.copyWith(color: color),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildChainArrow() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      child: const Icon(
        Icons.arrow_forward,
        color: AppColors.primaryRed,
        size: 20,
      ),
    );
  }

  Widget _buildPerformanceMetricsCard() {
    return ChimeraCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.speed,
                color: AppColors.primaryRed,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Performance',
                style: AppTextStyles.titleMedium,
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildMetricItem(
                  'Download',
                  '${_downloadSpeed.toStringAsFixed(1)} Mbps',
                  Icons.download,
                  AppColors.successGreen,
                ),
              ),
              Expanded(
                child: _buildMetricItem(
                  'Upload',
                  '${_uploadSpeed.toStringAsFixed(1)} Mbps',
                  Icons.upload,
                  AppColors.infoBlue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildMetricItem(
                  'Latency',
                  '${_calculateAverageLatency()}ms',
                  Icons.timer,
                  AppColors.warningOrange,
                ),
              ),
              Expanded(
                child: _buildMetricItem(
                  'Data Used',
                  _calculateDataTransferred(),
                  Icons.data_usage,
                  AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMetricItem(String label, String value, IconData icon, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: color),
            const SizedBox(width: 4),
            Text(label, style: AppTextStyles.labelSmall),
          ],
        ),
        const SizedBox(height: 4),
        Text(value, style: AppTextStyles.dataTransfer.copyWith(color: color)),
      ],
    );
  }

  Widget _buildSecurityStatusCard() {
    return ChimeraCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.security,
                color: AppColors.primaryRed,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Security Status',
                style: AppTextStyles.titleMedium,
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildSecurityItem(
            'DNS Protection',
            _isConnected ? 'Active' : 'Inactive',
            _isConnected ? StatusType.security : StatusType.security,
          ),
          const SizedBox(height: 8),
          _buildSecurityItem(
            'Kill Switch',
            'Enabled',
            StatusType.security,
          ),
          const SizedBox(height: 8),
          _buildSecurityItem(
            'IPv6 Protection',
            _isConnected ? 'Protected' : 'Exposed',
            StatusType.security,
          ),
          const SizedBox(height: 8),
          _buildSecurityItem(
            'Traffic Obfuscation',
            _isConnected ? 'Active' : 'Inactive',
            StatusType.security,
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityItem(String title, String status, StatusType type) {
    return Row(
      children: [
        Expanded(
          child: Text(
            title,
            style: AppTextStyles.bodyMedium,
          ),
        ),
        StatusIndicator(
          status: status,
          type: type,
          size: 16,
          showText: true,
        ),
      ],
    );
  }

  Widget _buildRecentActivityCard() {
    return ChimeraCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.history,
                color: AppColors.primaryRed,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Recent Activity',
                style: AppTextStyles.titleMedium,
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildActivityItem(
            'Connection established',
            'Chain: EU-Multi-Hop',
            '2 minutes ago',
            Icons.link,
            AppColors.successGreen,
          ),
          const SizedBox(height: 12),
          _buildActivityItem(
            'Server switched',
            'From DE-Berlin to NL-Amsterdam',
            '15 minutes ago',
            Icons.swap_horiz,
            AppColors.infoBlue,
          ),
          const SizedBox(height: 12),
          _buildActivityItem(
            'Security scan completed',
            'No threats detected',
            '1 hour ago',
            Icons.security,
            AppColors.successGreen,
          ),
        ],
      ),
    );
  }

  Widget _buildActivityItem(
    String title,
    String subtitle,
    String time,
    IconData icon,
    Color iconColor,
  ) {
    return Row(
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: iconColor.withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: iconColor,
            size: 16,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTextStyles.bodyMedium,
              ),
              Text(
                subtitle,
                style: AppTextStyles.bodySmall,
              ),
            ],
          ),
        ),
        Text(
          time,
          style: AppTextStyles.labelSmall,
        ),
      ],
    );
  }

  Future<void> _refreshStatus() async {
    // Simulate network call
    await Future.delayed(const Duration(seconds: 1));
    
    setState(() {
      // Update status with mock data
      if (_isConnected) {
        _uploadSpeed = (50 + (DateTime.now().millisecond % 50)).toDouble();
        _downloadSpeed = (100 + (DateTime.now().millisecond % 100)).toDouble();
      }
    });
  }

  void _toggleConnection() {
    setState(() {
      _isConnected = !_isConnected;
      if (_isConnected) {
        _connectionStatus = 'Connected';
        _currentChain = 'EU-Multi-Hop';
        _exitLocation = 'Netherlands, Amsterdam';
        _publicIP = '**************';
        _totalHops = 3;
        _uploadSpeed = 75.5;
        _downloadSpeed = 125.8;
      } else {
        _connectionStatus = 'Disconnected';
        _currentChain = 'No active chain';
        _exitLocation = 'Unknown';
        _publicIP = '*************';
        _totalHops = 0;
        _uploadSpeed = 0.0;
        _downloadSpeed = 0.0;
      }
    });
  }

  List<String> _generateMockHops() {
    if (!_isConnected) return [];
    return ['DE-Berlin', 'FR-Paris', 'NL-Amsterdam'];
  }

  int _calculateAverageLatency() {
    if (!_isConnected) return 0;
    return 45 + (DateTime.now().millisecond % 20);
  }

  String _calculateDataTransferred() {
    if (!_isConnected) return '0 MB';
    return '${(DateTime.now().millisecond % 500 + 100)} MB';
  }
}
