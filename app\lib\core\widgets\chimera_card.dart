import 'package:flutter/material.dart';
import '../theme/app_colors.dart';

/// Custom card widget with Chimera VPN styling
class ChimeraCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final VoidCallback? onTap;
  final bool showBorder;
  final bool showShadow;
  final Color? backgroundColor;
  final Color? borderColor;
  final double borderRadius;
  final double? elevation;

  const ChimeraCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.onTap,
    this.showBorder = true,
    this.showShadow = true,
    this.backgroundColor,
    this.borderColor,
    this.borderRadius = 12,
    this.elevation,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? const EdgeInsets.all(8),
      child: Material(
        color: backgroundColor ?? AppColors.backgroundCard,
        borderRadius: BorderRadius.circular(borderRadius),
        elevation: showShadow ? (elevation ?? 4) : 0,
        shadowColor: AppColors.primaryBlack,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(borderRadius),
          child: Container(
            padding: padding ?? const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(borderRadius),
              border: showBorder
                  ? Border.all(
                      color: borderColor ?? AppColors.borderPrimary,
                      width: 1,
                    )
                  : null,
            ),
            child: child,
          ),
        ),
      ),
    );
  }
}

/// Server card widget for displaying VPN server information
class ServerCard extends StatelessWidget {
  final String serverName;
  final String location;
  final String protocol;
  final int latency;
  final String trustLevel;
  final bool isSelected;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;

  const ServerCard({
    super.key,
    required this.serverName,
    required this.location,
    required this.protocol,
    required this.latency,
    required this.trustLevel,
    this.isSelected = false,
    this.onTap,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    return ChimeraCard(
      onTap: onTap,
      backgroundColor: isSelected 
          ? AppColors.primaryRed.withOpacity(0.1)
          : AppColors.backgroundCard,
      borderColor: isSelected 
          ? AppColors.primaryRed
          : AppColors.borderPrimary,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  serverName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),
              _buildTrustBadge(),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.location_on,
                size: 16,
                color: AppColors.textSecondary,
              ),
              const SizedBox(width: 4),
              Text(
                location,
                style: const TextStyle(
                  fontSize: 14,
                  color: AppColors.textSecondary,
                ),
              ),
              const Spacer(),
              _buildProtocolBadge(),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.speed,
                size: 16,
                color: _getLatencyColor(),
              ),
              const SizedBox(width: 4),
              Text(
                '${latency}ms',
                style: TextStyle(
                  fontSize: 14,
                  color: _getLatencyColor(),
                  fontFamily: 'RobotoMono',
                ),
              ),
              const Spacer(),
              if (isSelected)
                const Icon(
                  Icons.check_circle,
                  color: AppColors.primaryRed,
                  size: 20,
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTrustBadge() {
    Color badgeColor;
    IconData badgeIcon;

    switch (trustLevel.toLowerCase()) {
      case 'trusted':
        badgeColor = AppColors.trustedGreen;
        badgeIcon = Icons.verified;
        break;
      case 'community':
        badgeColor = AppColors.communityBlue;
        badgeIcon = Icons.people;
        break;
      case 'untrusted':
        badgeColor = AppColors.untrustedOrange;
        badgeIcon = Icons.warning;
        break;
      case 'flagged':
        badgeColor = AppColors.flaggedRed;
        badgeIcon = Icons.flag;
        break;
      default:
        badgeColor = AppColors.textSecondary;
        badgeIcon = Icons.help;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: badgeColor.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: badgeColor, width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            badgeIcon,
            size: 12,
            color: badgeColor,
          ),
          const SizedBox(width: 4),
          Text(
            trustLevel,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              color: badgeColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProtocolBadge() {
    Color protocolColor;
    switch (protocol.toLowerCase()) {
      case 'wireguard':
        protocolColor = AppColors.successGreen;
        break;
      case 'openvpn':
        protocolColor = AppColors.infoBlue;
        break;
      default:
        protocolColor = AppColors.textSecondary;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: protocolColor,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        protocol.toUpperCase(),
        style: const TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w600,
          color: AppColors.textOnRed,
        ),
      ),
    );
  }

  Color _getLatencyColor() {
    if (latency < 50) {
      return AppColors.successGreen;
    } else if (latency < 100) {
      return AppColors.warningOrange;
    } else {
      return AppColors.errorRed;
    }
  }
}

/// Connection chain card for displaying multi-hop chain information
class ChainCard extends StatelessWidget {
  final String chainName;
  final List<String> hops;
  final String exitLocation;
  final bool isActive;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const ChainCard({
    super.key,
    required this.chainName,
    required this.hops,
    required this.exitLocation,
    this.isActive = false,
    this.onTap,
    this.onEdit,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return ChimeraCard(
      onTap: onTap,
      backgroundColor: isActive 
          ? AppColors.primaryRed.withOpacity(0.1)
          : AppColors.backgroundCard,
      borderColor: isActive 
          ? AppColors.primaryRed
          : AppColors.borderPrimary,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  chainName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),
              if (isActive)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.connectedGreen,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'ACTIVE',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textOnRed,
                    ),
                  ),
                ),
              PopupMenuButton<String>(
                icon: const Icon(
                  Icons.more_vert,
                  color: AppColors.textSecondary,
                ),
                onSelected: (value) {
                  switch (value) {
                    case 'edit':
                      onEdit?.call();
                      break;
                    case 'delete':
                      onDelete?.call();
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 16),
                        SizedBox(width: 8),
                        Text('Edit'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 16, color: AppColors.errorRed),
                        SizedBox(width: 8),
                        Text('Delete', style: TextStyle(color: AppColors.errorRed)),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildChainVisualization(),
          const SizedBox(height: 8),
          Row(
            children: [
              const Icon(
                Icons.exit_to_app,
                size: 16,
                color: AppColors.textSecondary,
              ),
              const SizedBox(width: 4),
              Text(
                'Exit: $exitLocation',
                style: const TextStyle(
                  fontSize: 14,
                  color: AppColors.textSecondary,
                ),
              ),
              const Spacer(),
              Text(
                '${hops.length} hops',
                style: const TextStyle(
                  fontSize: 12,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildChainVisualization() {
    return Row(
      children: [
        const Icon(
          Icons.computer,
          size: 16,
          color: AppColors.textSecondary,
        ),
        ...hops.asMap().entries.map((entry) {
          final index = entry.key;
          final hop = entry.value;
          return Row(
            children: [
              const SizedBox(width: 8),
              const Icon(
                Icons.arrow_forward,
                size: 12,
                color: AppColors.primaryRed,
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: AppColors.primaryRed.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: AppColors.primaryRed, width: 1),
                ),
                child: Text(
                  hop,
                  style: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: AppColors.primaryRed,
                  ),
                ),
              ),
            ],
          );
        }).toList(),
        const SizedBox(width: 8),
        const Icon(
          Icons.arrow_forward,
          size: 12,
          color: AppColors.primaryRed,
        ),
        const SizedBox(width: 8),
        const Icon(
          Icons.public,
          size: 16,
          color: AppColors.successGreen,
        ),
      ],
    );
  }
}
