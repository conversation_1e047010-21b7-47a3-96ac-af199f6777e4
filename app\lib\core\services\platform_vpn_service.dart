import 'dart:async';
import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:process_run/process_run.dart';
import '../models/vpn_connection.dart';
import '../models/server_model.dart';

/// Platform-specific VPN implementation using system VPN capabilities
class PlatformVPNService {
  static const String _tag = 'PlatformVPNService';
  
  Process? _vpnProcess;
  Timer? _healthCheckTimer;
  
  /// Check if platform supports VPN
  bool get isSupported {
    if (kIsWeb) return false;
    return Platform.isWindows || Platform.isMacOS || Platform.isLinux;
  }
  
  /// Connect to VPN server using platform-specific method
  Future<bool> connectToServer(VpnServer server, VpnProtocol protocol) async {
    try {
      debugPrint('$_tag: Connecting to ${server.name} using ${protocol.name}');
      
      if (Platform.isWindows) {
        return await _connectWindows(server, protocol);
      } else if (Platform.isMacOS) {
        return await _connectMacOS(server, protocol);
      } else if (Platform.isLinux) {
        return await _connectLinux(server, protocol);
      }
      
      return false;
    } catch (e) {
      debugPrint('$_tag: Connection failed: $e');
      return false;
    }
  }
  
  /// Disconnect from VPN
  Future<bool> disconnect() async {
    try {
      debugPrint('$_tag: Disconnecting VPN');
      
      _healthCheckTimer?.cancel();
      
      if (_vpnProcess != null) {
        _vpnProcess!.kill();
        _vpnProcess = null;
      }
      
      if (Platform.isWindows) {
        return await _disconnectWindows();
      } else if (Platform.isMacOS) {
        return await _disconnectMacOS();
      } else if (Platform.isLinux) {
        return await _disconnectLinux();
      }
      
      return true;
    } catch (e) {
      debugPrint('$_tag: Disconnection failed: $e');
      return false;
    }
  }
  
  /// Check VPN connection status
  Future<bool> isConnected() async {
    try {
      if (Platform.isWindows) {
        return await _checkConnectionWindows();
      } else if (Platform.isMacOS) {
        return await _checkConnectionMacOS();
      } else if (Platform.isLinux) {
        return await _checkConnectionLinux();
      }
      
      return false;
    } catch (e) {
      debugPrint('$_tag: Status check failed: $e');
      return false;
    }
  }
  
  /// Get current VPN IP address
  Future<String?> getVPNIP() async {
    try {
      // Check for VPN interface IP
      if (Platform.isWindows) {
        final result = await Process.run('ipconfig', []);
        final output = result.stdout.toString();
        
        // Look for VPN adapter IP
        final vpnMatch = RegExp(r'PPP adapter.*?IPv4 Address.*?:\s*(\d+\.\d+\.\d+\.\d+)')
            .firstMatch(output);
        if (vpnMatch != null) {
          return vpnMatch.group(1);
        }
      } else {
        final result = await Process.run('ifconfig', []);
        final output = result.stdout.toString();
        
        // Look for tun/tap interface
        final vpnMatch = RegExp(r'(tun|tap)\d+.*?inet (\d+\.\d+\.\d+\.\d+)')
            .firstMatch(output);
        if (vpnMatch != null) {
          return vpnMatch.group(2);
        }
      }
      
      return null;
    } catch (e) {
      debugPrint('$_tag: Failed to get VPN IP: $e');
      return null;
    }
  }
  
  // Windows-specific implementations
  
  Future<bool> _connectWindows(VpnServer server, VpnProtocol protocol) async {
    try {
      if (protocol == VpnProtocol.wireguard) {
        return await _connectWireGuardWindows(server);
      } else {
        return await _connectOpenVPNWindows(server, protocol);
      }
    } catch (e) {
      debugPrint('$_tag: Windows connection failed: $e');
      return false;
    }
  }
  
  Future<bool> _connectWireGuardWindows(VpnServer server) async {
    try {
      // Check if WireGuard is installed
      final wgPath = await _findWireGuardPath();
      if (wgPath == null) {
        debugPrint('$_tag: WireGuard not found on Windows');
        return false;
      }
      
      // Create temporary config file
      final configFile = await _createWireGuardConfig(server);
      
      // Start WireGuard connection
      final result = await Process.run(wgPath, ['up', configFile.path]);
      
      if (result.exitCode == 0) {
        debugPrint('$_tag: WireGuard connected successfully');
        _startHealthCheck();
        return true;
      } else {
        debugPrint('$_tag: WireGuard connection failed: ${result.stderr}');
        return false;
      }
    } catch (e) {
      debugPrint('$_tag: WireGuard Windows connection error: $e');
      return false;
    }
  }
  
  Future<bool> _connectOpenVPNWindows(VpnServer server, VpnProtocol protocol) async {
    try {
      // Check if OpenVPN is installed
      final ovpnPath = await _findOpenVPNPath();
      if (ovpnPath == null) {
        debugPrint('$_tag: OpenVPN not found on Windows');
        return false;
      }
      
      // Create temporary config file
      final configFile = await _createOpenVPNConfig(server, protocol);
      
      // Start OpenVPN connection
      _vpnProcess = await Process.start(ovpnPath, [
        '--config', configFile.path,
        '--verb', '3',
      ]);
      
      // Monitor connection establishment
      bool connected = false;
      final completer = Completer<bool>();
      
      _vpnProcess!.stdout.transform(utf8.decoder).listen((data) {
        debugPrint('$_tag: OpenVPN: $data');
        if (data.contains('Initialization Sequence Completed')) {
          connected = true;
          if (!completer.isCompleted) completer.complete(true);
        }
      });
      
      _vpnProcess!.stderr.transform(utf8.decoder).listen((data) {
        debugPrint('$_tag: OpenVPN Error: $data');
        if (data.contains('FATAL') && !completer.isCompleted) {
          completer.complete(false);
        }
      });
      
      // Wait for connection or timeout
      Timer(const Duration(seconds: 30), () {
        if (!completer.isCompleted) completer.complete(false);
      });
      
      final result = await completer.future;
      if (result) {
        _startHealthCheck();
      }
      
      return result;
    } catch (e) {
      debugPrint('$_tag: OpenVPN Windows connection error: $e');
      return false;
    }
  }
  
  Future<bool> _disconnectWindows() async {
    try {
      // Try to disconnect WireGuard
      final wgPath = await _findWireGuardPath();
      if (wgPath != null) {
        await Process.run(wgPath, ['down']);
      }
      
      // Kill any OpenVPN processes
      await Process.run('taskkill', ['/F', '/IM', 'openvpn.exe']);
      
      return true;
    } catch (e) {
      debugPrint('$_tag: Windows disconnection error: $e');
      return false;
    }
  }
  
  Future<bool> _checkConnectionWindows() async {
    try {
      // Check for VPN network interfaces
      final result = await Process.run('ipconfig', []);
      final output = result.stdout.toString();
      
      return output.contains('PPP adapter') || 
             output.contains('WireGuard') ||
             output.contains('TAP-Windows');
    } catch (e) {
      return false;
    }
  }
  
  // macOS-specific implementations
  
  Future<bool> _connectMacOS(VpnServer server, VpnProtocol protocol) async {
    // Similar implementation for macOS using networksetup and system VPN
    debugPrint('$_tag: macOS VPN connection not yet implemented');
    return false;
  }
  
  Future<bool> _disconnectMacOS() async {
    debugPrint('$_tag: macOS VPN disconnection not yet implemented');
    return false;
  }
  
  Future<bool> _checkConnectionMacOS() async {
    try {
      final result = await Process.run('ifconfig', []);
      final output = result.stdout.toString();
      return output.contains('utun') || output.contains('tun');
    } catch (e) {
      return false;
    }
  }
  
  // Linux-specific implementations
  
  Future<bool> _connectLinux(VpnServer server, VpnProtocol protocol) async {
    // Similar implementation for Linux using NetworkManager or direct tools
    debugPrint('$_tag: Linux VPN connection not yet implemented');
    return false;
  }
  
  Future<bool> _disconnectLinux() async {
    debugPrint('$_tag: Linux VPN disconnection not yet implemented');
    return false;
  }
  
  Future<bool> _checkConnectionLinux() async {
    try {
      final result = await Process.run('ifconfig', []);
      final output = result.stdout.toString();
      return output.contains('tun') || output.contains('tap');
    } catch (e) {
      return false;
    }
  }
  
  // Helper methods
  
  Future<String?> _findWireGuardPath() async {
    final possiblePaths = [
      'C:\\Program Files\\WireGuard\\wireguard.exe',
      'C:\\Program Files (x86)\\WireGuard\\wireguard.exe',
      'wg', // If in PATH
    ];
    
    for (final path in possiblePaths) {
      try {
        final result = await Process.run(path, ['--version']);
        if (result.exitCode == 0) return path;
      } catch (e) {
        continue;
      }
    }
    
    return null;
  }
  
  Future<String?> _findOpenVPNPath() async {
    final possiblePaths = [
      'C:\\Program Files\\OpenVPN\\bin\\openvpn.exe',
      'C:\\Program Files (x86)\\OpenVPN\\bin\\openvpn.exe',
      'openvpn', // If in PATH
    ];
    
    for (final path in possiblePaths) {
      try {
        final result = await Process.run(path, ['--version']);
        if (result.exitCode == 0) return path;
      } catch (e) {
        continue;
      }
    }
    
    return null;
  }
  
  Future<File> _createWireGuardConfig(VpnServer server) async {
    // Create WireGuard configuration file
    final config = '''
[Interface]
PrivateKey = ${server.credentials?.privateKey ?? 'PLACEHOLDER_PRIVATE_KEY'}
Address = ********/24
DNS = *******

[Peer]
PublicKey = ${server.credentials?.publicKey ?? 'PLACEHOLDER_PUBLIC_KEY'}
Endpoint = ${server.ipAddress}:${server.port}
AllowedIPs = 0.0.0.0/0
''';
    
    final tempDir = Directory.systemTemp;
    final configFile = File('${tempDir.path}/chimera_wg_${server.id}.conf');
    await configFile.writeAsString(config);
    
    return configFile;
  }
  
  Future<File> _createOpenVPNConfig(VpnServer server, VpnProtocol protocol) async {
    // Create OpenVPN configuration file
    final protocolStr = protocol == VpnProtocol.openVpnUdp ? 'udp' : 'tcp';
    
    final config = '''
client
dev tun
proto $protocolStr
remote ${server.ipAddress} ${server.port}
resolv-retry infinite
nobind
persist-key
persist-tun
ca ca.crt
cert client.crt
key client.key
verb 3
''';
    
    final tempDir = Directory.systemTemp;
    final configFile = File('${tempDir.path}/chimera_ovpn_${server.id}.ovpn');
    await configFile.writeAsString(config);
    
    return configFile;
  }
  
  void _startHealthCheck() {
    _healthCheckTimer?.cancel();
    _healthCheckTimer = Timer.periodic(const Duration(seconds: 30), (timer) async {
      final connected = await isConnected();
      if (!connected) {
        debugPrint('$_tag: Health check failed - connection lost');
        timer.cancel();
      }
    });
  }
  
  void dispose() {
    _healthCheckTimer?.cancel();
    _vpnProcess?.kill();
  }
}
