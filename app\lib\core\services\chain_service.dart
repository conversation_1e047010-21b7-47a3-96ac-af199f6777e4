import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import '../models/server_model.dart';
import '../models/vpn_connection.dart';
import 'platform_vpn_service.dart';
import 'security_service.dart';

/// Multi-hop VPN chain management service
class ChainService {
  static const String _tag = 'ChainService';
  
  final PlatformVPNService _platformVPN = PlatformVPNService();
  final SecurityService _securityService = SecurityService();
  final Uuid _uuid = const Uuid();
  
  final List<VpnChain> _chains = [];
  VpnChain? _activeChain;
  
  final StreamController<List<VpnChain>> _chainsController = 
      StreamController<List<VpnChain>>.broadcast();
  final StreamController<VpnChain?> _activeChainController = 
      StreamController<VpnChain?>.broadcast();
  final StreamController<ChainConnectionStatus> _statusController = 
      StreamController<ChainConnectionStatus>.broadcast();
  
  Stream<List<VpnChain>> get chainsStream => _chainsController.stream;
  Stream<VpnChain?> get activeChainStream => _activeChainController.stream;
  Stream<ChainConnectionStatus> get statusStream => _statusController.stream;
  
  List<VpnChain> get chains => List.unmodifiable(_chains);
  VpnChain? get activeChain => _activeChain;
  
  /// Initialize chain service
  Future<void> initialize() async {
    debugPrint('$_tag: Initializing chain service');
    await _loadSavedChains();
  }
  
  /// Create a new VPN chain
  VpnChain createChain({
    required String name,
    required List<VpnServer> servers,
    String? description,
    bool torExit = false,
    bool obfuscateFirstHop = false,
  }) {
    final chain = VpnChain(
      id: _uuid.v4(),
      name: name,
      description: description ?? '',
      hops: servers.map((server) => ChainHop(
        id: _uuid.v4(),
        server: server,
        protocol: server.protocol,
        obfuscated: obfuscateFirstHop && servers.first == server,
      )).toList(),
      torExit: torExit,
      createdAt: DateTime.now(),
    );
    
    _chains.add(chain);
    _chainsController.add(_chains);
    _saveChainsToStorage();
    
    debugPrint('$_tag: Created chain "${chain.name}" with ${chain.hops.length} hops');
    return chain;
  }
  
  /// Connect to a multi-hop chain
  Future<bool> connectToChain(VpnChain chain) async {
    try {
      debugPrint('$_tag: Connecting to chain "${chain.name}"');
      
      if (_activeChain != null) {
        await disconnectChain();
      }
      
      _updateStatus(ChainConnectionStatus.connecting(chain));
      
      // Validate chain before connecting
      final validationResult = await _validateChain(chain);
      if (!validationResult.isValid) {
        _updateStatus(ChainConnectionStatus.failed(
          chain, 
          'Chain validation failed: ${validationResult.error}'
        ));
        return false;
      }
      
      // Connect hops sequentially
      final connectedHops = <ChainHop>[];
      
      for (int i = 0; i < chain.hops.length; i++) {
        final hop = chain.hops[i];
        debugPrint('$_tag: Connecting hop ${i + 1}/${chain.hops.length}: ${hop.server.name}');
        
        _updateStatus(ChainConnectionStatus.connectingHop(chain, i + 1, hop.server.name));
        
        final success = await _connectHop(hop, connectedHops);
        if (!success) {
          // Rollback previous connections
          await _rollbackConnections(connectedHops);
          _updateStatus(ChainConnectionStatus.failed(
            chain, 
            'Failed to connect to hop ${i + 1}: ${hop.server.name}'
          ));
          return false;
        }
        
        connectedHops.add(hop);
        
        // Wait between hops to ensure stability
        await Future.delayed(const Duration(seconds: 2));
      }
      
      // If Tor exit is enabled, establish Tor connection
      if (chain.torExit) {
        debugPrint('$_tag: Establishing Tor exit connection');
        _updateStatus(ChainConnectionStatus.connectingTor(chain));
        
        final torSuccess = await _establishTorConnection();
        if (!torSuccess) {
          await _rollbackConnections(connectedHops);
          _updateStatus(ChainConnectionStatus.failed(chain, 'Failed to establish Tor connection'));
          return false;
        }
      }
      
      // Chain successfully connected
      _activeChain = chain.copyWith(
        lastConnected: DateTime.now(),
        connectionCount: chain.connectionCount + 1,
      );
      
      _updateChainInList(_activeChain!);
      _activeChainController.add(_activeChain);
      _updateStatus(ChainConnectionStatus.connected(chain));
      
      // Start chain monitoring
      _startChainMonitoring();
      
      debugPrint('$_tag: Successfully connected to chain "${chain.name}"');
      return true;
      
    } catch (e) {
      debugPrint('$_tag: Chain connection failed: $e');
      _updateStatus(ChainConnectionStatus.failed(chain, e.toString()));
      return false;
    }
  }
  
  /// Disconnect from current chain
  Future<bool> disconnectChain() async {
    try {
      if (_activeChain == null) {
        debugPrint('$_tag: No active chain to disconnect');
        return true;
      }
      
      debugPrint('$_tag: Disconnecting from chain "${_activeChain!.name}"');
      _updateStatus(ChainConnectionStatus.disconnecting(_activeChain!));
      
      // Stop monitoring
      _stopChainMonitoring();
      
      // Disconnect from VPN
      await _platformVPN.disconnect();
      
      // Disable security features if needed
      await _securityService.disableKillSwitch();
      
      _activeChain = null;
      _activeChainController.add(null);
      _updateStatus(ChainConnectionStatus.disconnected());
      
      debugPrint('$_tag: Successfully disconnected from chain');
      return true;
      
    } catch (e) {
      debugPrint('$_tag: Chain disconnection failed: $e');
      return false;
    }
  }
  
  /// Test a chain's connectivity
  Future<ChainTestResult> testChain(VpnChain chain) async {
    debugPrint('$_tag: Testing chain "${chain.name}"');
    
    final results = <HopTestResult>[];
    
    for (int i = 0; i < chain.hops.length; i++) {
      final hop = chain.hops[i];
      final startTime = DateTime.now();
      
      try {
        final success = await _platformVPN.testConnection(hop.server, hop.protocol);
        final latency = DateTime.now().difference(startTime).inMilliseconds;
        
        results.add(HopTestResult(
          hopIndex: i,
          server: hop.server,
          success: success,
          latency: success ? latency : null,
          error: success ? null : 'Connection failed',
        ));
        
      } catch (e) {
        results.add(HopTestResult(
          hopIndex: i,
          server: hop.server,
          success: false,
          latency: null,
          error: e.toString(),
        ));
      }
    }
    
    final overallSuccess = results.every((result) => result.success);
    final averageLatency = results
        .where((result) => result.latency != null)
        .map((result) => result.latency!)
        .fold<int>(0, (sum, latency) => sum + latency) / 
        results.where((result) => result.latency != null).length;
    
    return ChainTestResult(
      chain: chain,
      success: overallSuccess,
      averageLatency: averageLatency.isNaN ? null : averageLatency.round(),
      hopResults: results,
      timestamp: DateTime.now(),
    );
  }
  
  /// Delete a chain
  bool deleteChain(String chainId) {
    final index = _chains.indexWhere((chain) => chain.id == chainId);
    if (index == -1) return false;
    
    final chain = _chains[index];
    
    // Don't delete active chain
    if (_activeChain?.id == chainId) {
      debugPrint('$_tag: Cannot delete active chain');
      return false;
    }
    
    _chains.removeAt(index);
    _chainsController.add(_chains);
    _saveChainsToStorage();
    
    debugPrint('$_tag: Deleted chain "${chain.name}"');
    return true;
  }
  
  /// Update an existing chain
  bool updateChain(VpnChain updatedChain) {
    final index = _chains.indexWhere((chain) => chain.id == updatedChain.id);
    if (index == -1) return false;
    
    _chains[index] = updatedChain;
    _chainsController.add(_chains);
    _saveChainsToStorage();
    
    if (_activeChain?.id == updatedChain.id) {
      _activeChain = updatedChain;
      _activeChainController.add(_activeChain);
    }
    
    return true;
  }
  
  // Private methods
  
  Future<ChainValidationResult> _validateChain(VpnChain chain) async {
    if (chain.hops.isEmpty) {
      return ChainValidationResult(false, 'Chain must have at least one hop');
    }
    
    if (chain.hops.length > 5) {
      return ChainValidationResult(false, 'Chain cannot have more than 5 hops');
    }
    
    // Check for duplicate servers
    final serverIds = chain.hops.map((hop) => hop.server.id).toSet();
    if (serverIds.length != chain.hops.length) {
      return ChainValidationResult(false, 'Chain cannot use the same server multiple times');
    }
    
    return ChainValidationResult(true, null);
  }
  
  Future<bool> _connectHop(ChainHop hop, List<ChainHop> previousHops) async {
    try {
      // For the first hop, connect directly
      if (previousHops.isEmpty) {
        return await _platformVPN.connectToServer(hop.server, hop.protocol);
      }
      
      // For subsequent hops, we need to route through previous hops
      // This is a simplified implementation - in reality, this would require
      // more complex routing configuration
      return await _platformVPN.connectToServer(hop.server, hop.protocol);
      
    } catch (e) {
      debugPrint('$_tag: Hop connection failed: $e');
      return false;
    }
  }
  
  Future<void> _rollbackConnections(List<ChainHop> connectedHops) async {
    debugPrint('$_tag: Rolling back ${connectedHops.length} connections');
    
    // Disconnect in reverse order
    for (final hop in connectedHops.reversed) {
      try {
        await _platformVPN.disconnect();
      } catch (e) {
        debugPrint('$_tag: Rollback failed for hop ${hop.server.name}: $e');
      }
    }
  }
  
  Future<bool> _establishTorConnection() async {
    try {
      // In a real implementation, this would start a Tor process
      // and configure it as the final exit point
      debugPrint('$_tag: Tor connection establishment not yet implemented');
      return true; // Simulate success for now
    } catch (e) {
      debugPrint('$_tag: Tor connection failed: $e');
      return false;
    }
  }
  
  Timer? _monitoringTimer;
  
  void _startChainMonitoring() {
    _monitoringTimer?.cancel();
    _monitoringTimer = Timer.periodic(const Duration(seconds: 30), (timer) async {
      if (_activeChain == null) {
        timer.cancel();
        return;
      }
      
      // Check chain health
      final isHealthy = await _checkChainHealth();
      if (!isHealthy) {
        debugPrint('$_tag: Chain health check failed');
        // Attempt reconnection or notify user
      }
    });
  }
  
  void _stopChainMonitoring() {
    _monitoringTimer?.cancel();
  }
  
  Future<bool> _checkChainHealth() async {
    if (_activeChain == null) return false;
    
    try {
      // Check if VPN is still connected
      final isConnected = await _platformVPN.isConnected();
      return isConnected;
    } catch (e) {
      debugPrint('$_tag: Health check failed: $e');
      return false;
    }
  }
  
  void _updateStatus(ChainConnectionStatus status) {
    _statusController.add(status);
  }
  
  void _updateChainInList(VpnChain updatedChain) {
    final index = _chains.indexWhere((chain) => chain.id == updatedChain.id);
    if (index != -1) {
      _chains[index] = updatedChain;
      _chainsController.add(_chains);
    }
  }
  
  Future<void> _loadSavedChains() async {
    // Load chains from storage
    // Implementation would use Hive or SharedPreferences
    debugPrint('$_tag: Loading saved chains');
  }
  
  Future<void> _saveChainsToStorage() async {
    // Save chains to storage
    // Implementation would use Hive or SharedPreferences
    debugPrint('$_tag: Saving chains to storage');
  }
  
  void dispose() {
    _stopChainMonitoring();
    _chainsController.close();
    _activeChainController.close();
    _statusController.close();
    _platformVPN.dispose();
  }
}

// Data classes

class VpnChain {
  final String id;
  final String name;
  final String description;
  final List<ChainHop> hops;
  final bool torExit;
  final DateTime createdAt;
  final DateTime? lastConnected;
  final int connectionCount;
  
  VpnChain({
    required this.id,
    required this.name,
    required this.description,
    required this.hops,
    this.torExit = false,
    required this.createdAt,
    this.lastConnected,
    this.connectionCount = 0,
  });
  
  VpnChain copyWith({
    String? id,
    String? name,
    String? description,
    List<ChainHop>? hops,
    bool? torExit,
    DateTime? createdAt,
    DateTime? lastConnected,
    int? connectionCount,
  }) {
    return VpnChain(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      hops: hops ?? this.hops,
      torExit: torExit ?? this.torExit,
      createdAt: createdAt ?? this.createdAt,
      lastConnected: lastConnected ?? this.lastConnected,
      connectionCount: connectionCount ?? this.connectionCount,
    );
  }
  
  String get exitLocation {
    if (torExit) return 'Tor Network';
    if (hops.isNotEmpty) return hops.last.server.location;
    return 'Unknown';
  }
}

class ChainHop {
  final String id;
  final VpnServer server;
  final VpnProtocol protocol;
  final bool obfuscated;
  
  ChainHop({
    required this.id,
    required this.server,
    required this.protocol,
    this.obfuscated = false,
  });
}

class ChainConnectionStatus {
  final ChainConnectionState state;
  final VpnChain? chain;
  final String? message;
  final int? currentHop;
  final String? currentHopName;
  
  ChainConnectionStatus({
    required this.state,
    this.chain,
    this.message,
    this.currentHop,
    this.currentHopName,
  });
  
  factory ChainConnectionStatus.disconnected() {
    return ChainConnectionStatus(state: ChainConnectionState.disconnected);
  }
  
  factory ChainConnectionStatus.connecting(VpnChain chain) {
    return ChainConnectionStatus(
      state: ChainConnectionState.connecting,
      chain: chain,
      message: 'Connecting to chain "${chain.name}"',
    );
  }
  
  factory ChainConnectionStatus.connectingHop(VpnChain chain, int hopNumber, String hopName) {
    return ChainConnectionStatus(
      state: ChainConnectionState.connectingHop,
      chain: chain,
      currentHop: hopNumber,
      currentHopName: hopName,
      message: 'Connecting to hop $hopNumber: $hopName',
    );
  }
  
  factory ChainConnectionStatus.connectingTor(VpnChain chain) {
    return ChainConnectionStatus(
      state: ChainConnectionState.connectingTor,
      chain: chain,
      message: 'Establishing Tor connection',
    );
  }
  
  factory ChainConnectionStatus.connected(VpnChain chain) {
    return ChainConnectionStatus(
      state: ChainConnectionState.connected,
      chain: chain,
      message: 'Connected to chain "${chain.name}"',
    );
  }
  
  factory ChainConnectionStatus.disconnecting(VpnChain chain) {
    return ChainConnectionStatus(
      state: ChainConnectionState.disconnecting,
      chain: chain,
      message: 'Disconnecting from chain "${chain.name}"',
    );
  }
  
  factory ChainConnectionStatus.failed(VpnChain chain, String error) {
    return ChainConnectionStatus(
      state: ChainConnectionState.failed,
      chain: chain,
      message: error,
    );
  }
}

enum ChainConnectionState {
  disconnected,
  connecting,
  connectingHop,
  connectingTor,
  connected,
  disconnecting,
  failed,
}

class ChainValidationResult {
  final bool isValid;
  final String? error;
  
  ChainValidationResult(this.isValid, this.error);
}

class ChainTestResult {
  final VpnChain chain;
  final bool success;
  final int? averageLatency;
  final List<HopTestResult> hopResults;
  final DateTime timestamp;
  
  ChainTestResult({
    required this.chain,
    required this.success,
    this.averageLatency,
    required this.hopResults,
    required this.timestamp,
  });
}

class HopTestResult {
  final int hopIndex;
  final VpnServer server;
  final bool success;
  final int? latency;
  final String? error;
  
  HopTestResult({
    required this.hopIndex,
    required this.server,
    required this.success,
    this.latency,
    this.error,
  });
}
