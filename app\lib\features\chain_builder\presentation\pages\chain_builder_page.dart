import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/chimera_card.dart';
import '../../../../core/widgets/chimera_button.dart';

/// Chain builder page for creating and managing VPN chains
class ChainBuilderPage extends ConsumerStatefulWidget {
  const ChainBuilderPage({super.key});

  @override
  ConsumerState<ChainBuilderPage> createState() => _ChainBuilderPageState();
}

class _ChainBuilderPageState extends ConsumerState<ChainBuilderPage> {
  final List<ChainCard> _savedChains = [
    ChainCard(
      chainName: 'EU Multi-Hop',
      hops: ['DE-Berlin', 'FR-Paris', 'NL-Amsterdam'],
      exitLocation: 'Netherlands',
      isActive: true,
    ),
    ChainCard(
      chainName: 'Privacy Chain',
      hops: ['CH-Zurich', 'SE-Stockholm'],
      exitLocation: 'Sweden',
      isActive: false,
    ),
    ChainCard(
      chainName: 'Speed Optimized',
      hops: ['US-NewYork'],
      exitLocation: 'United States',
      isActive: false,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Chain Builder'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _createNewChain,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            _buildHeaderSection(),
            
            const SizedBox(height: 24),
            
            // Quick Start Section
            _buildQuickStartSection(),
            
            const SizedBox(height: 24),
            
            // Saved Chains Section
            _buildSavedChainsSection(),
            
            const SizedBox(height: 24),
            
            // Chain Templates Section
            _buildChainTemplatesSection(),
            
            const SizedBox(height: 32),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _createNewChain,
        backgroundColor: AppColors.primaryRed,
        foregroundColor: AppColors.textOnRed,
        icon: const Icon(Icons.add),
        label: const Text('New Chain'),
      ),
    );
  }

  Widget _buildHeaderSection() {
    return ChimeraCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: AppColors.primaryRed.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.account_tree,
                  color: AppColors.primaryRed,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Build Your Chain',
                      style: AppTextStyles.headlineSmall,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Create custom multi-hop VPN chains for enhanced privacy and security',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStartSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Start',
          style: AppTextStyles.titleLarge,
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: ChimeraButton(
                text: 'Auto-Build Chain',
                type: ChimeraButtonType.primary,
                icon: Icons.auto_fix_high,
                onPressed: _autoBuildChain,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ChimeraButton(
                text: 'Manual Builder',
                type: ChimeraButtonType.outline,
                icon: Icons.build,
                onPressed: _openManualBuilder,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSavedChainsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Saved Chains',
              style: AppTextStyles.titleLarge,
            ),
            const Spacer(),
            TextButton(
              onPressed: () {
                // TODO: Show all chains
              },
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: 12),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _savedChains.length,
          separatorBuilder: (context, index) => const SizedBox(height: 8),
          itemBuilder: (context, index) {
            final chain = _savedChains[index];
            return ChainCard(
              chainName: chain.chainName,
              hops: chain.hops,
              exitLocation: chain.exitLocation,
              isActive: chain.isActive,
              onTap: () => _selectChain(chain),
              onEdit: () => _editChain(chain),
              onDelete: () => _deleteChain(chain),
            );
          },
        ),
      ],
    );
  }

  Widget _buildChainTemplatesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Chain Templates',
          style: AppTextStyles.titleLarge,
        ),
        const SizedBox(height: 12),
        _buildTemplateCard(
          'Privacy Focused',
          'Multi-hop through privacy-friendly countries',
          Icons.privacy_tip,
          AppColors.successGreen,
          () => _useTemplate('privacy'),
        ),
        const SizedBox(height: 8),
        _buildTemplateCard(
          'Speed Optimized',
          'Single hop for maximum performance',
          Icons.speed,
          AppColors.infoBlue,
          () => _useTemplate('speed'),
        ),
        const SizedBox(height: 8),
        _buildTemplateCard(
          'Tor Integration',
          'Chain ending with Tor network',
          Icons.security,
          AppColors.warningOrange,
          () => _useTemplate('tor'),
        ),
      ],
    );
  }

  Widget _buildTemplateCard(
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return ChimeraCard(
      onTap: onTap,
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: AppTextStyles.titleSmall),
                Text(
                  description,
                  style: AppTextStyles.bodySmall,
                ),
              ],
            ),
          ),
          const Icon(
            Icons.arrow_forward_ios,
            color: AppColors.textSecondary,
            size: 16,
          ),
        ],
      ),
    );
  }

  void _createNewChain() {
    // TODO: Navigate to chain creation wizard
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Chain creation wizard coming soon!')),
    );
  }

  void _autoBuildChain() {
    // TODO: Implement auto chain building
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Auto-building optimal chain...')),
    );
  }

  void _openManualBuilder() {
    // TODO: Navigate to manual chain builder
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Manual builder coming soon!')),
    );
  }

  void _selectChain(ChainCard chain) {
    // TODO: Activate selected chain
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Activating ${chain.chainName}...')),
    );
  }

  void _editChain(ChainCard chain) {
    // TODO: Edit chain configuration
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Editing ${chain.chainName}...')),
    );
  }

  void _deleteChain(ChainCard chain) {
    // TODO: Delete chain with confirmation
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Deleting ${chain.chainName}...')),
    );
  }

  void _useTemplate(String templateType) {
    // TODO: Create chain from template
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Creating chain from $templateType template...')),
    );
  }
}
