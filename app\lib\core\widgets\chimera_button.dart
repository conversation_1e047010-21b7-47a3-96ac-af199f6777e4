import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';

/// Custom button widget with Chimera VPN styling
class ChimeraButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final ChimeraButtonType type;
  final ChimeraButtonSize size;
  final IconData? icon;
  final bool isLoading;
  final bool isFullWidth;

  const ChimeraButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = ChimeraButtonType.primary,
    this.size = ChimeraButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isFullWidth = false,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: isFullWidth ? double.infinity : null,
      height: _getHeight(),
      child: _buildButton(),
    );
  }

  Widget _buildButton() {
    switch (type) {
      case ChimeraButtonType.primary:
        return _buildPrimaryButton();
      case ChimeraButtonType.secondary:
        return _buildSecondaryButton();
      case ChimeraButtonType.outline:
        return _buildOutlineButton();
      case ChimeraButtonType.text:
        return _buildTextButton();
      case ChimeraButtonType.danger:
        return _buildDangerButton();
    }
  }

  Widget _buildPrimaryButton() {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primaryRed,
        foregroundColor: AppColors.textOnRed,
        elevation: 2,
        shadowColor: AppColors.primaryRedDark,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        padding: _getPadding(),
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildSecondaryButton() {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.backgroundSecondary,
        foregroundColor: AppColors.textPrimary,
        elevation: 1,
        shadowColor: AppColors.primaryBlack,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: const BorderSide(color: AppColors.borderPrimary),
        ),
        padding: _getPadding(),
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildOutlineButton() {
    return OutlinedButton(
      onPressed: isLoading ? null : onPressed,
      style: OutlinedButton.styleFrom(
        foregroundColor: AppColors.primaryRed,
        side: const BorderSide(color: AppColors.primaryRed, width: 1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        padding: _getPadding(),
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildTextButton() {
    return TextButton(
      onPressed: isLoading ? null : onPressed,
      style: TextButton.styleFrom(
        foregroundColor: AppColors.primaryRed,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        padding: _getPadding(),
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildDangerButton() {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.errorRed,
        foregroundColor: AppColors.textOnRed,
        elevation: 2,
        shadowColor: AppColors.errorRed.withOpacity(0.5),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        padding: _getPadding(),
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildButtonContent() {
    if (isLoading) {
      return SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            type == ChimeraButtonType.outline || type == ChimeraButtonType.text
                ? AppColors.primaryRed
                : AppColors.textOnRed,
          ),
        ),
      );
    }

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: _getIconSize()),
          const SizedBox(width: 8),
          Text(text, style: _getTextStyle()),
        ],
      );
    }

    return Text(text, style: _getTextStyle());
  }

  double _getHeight() {
    switch (size) {
      case ChimeraButtonSize.small:
        return 32;
      case ChimeraButtonSize.medium:
        return 40;
      case ChimeraButtonSize.large:
        return 48;
    }
  }

  EdgeInsets _getPadding() {
    switch (size) {
      case ChimeraButtonSize.small:
        return const EdgeInsets.symmetric(horizontal: 12, vertical: 6);
      case ChimeraButtonSize.medium:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 8);
      case ChimeraButtonSize.large:
        return const EdgeInsets.symmetric(horizontal: 20, vertical: 12);
    }
  }

  double _getIconSize() {
    switch (size) {
      case ChimeraButtonSize.small:
        return 16;
      case ChimeraButtonSize.medium:
        return 18;
      case ChimeraButtonSize.large:
        return 20;
    }
  }

  TextStyle _getTextStyle() {
    switch (size) {
      case ChimeraButtonSize.small:
        return AppTextStyles.labelSmall;
      case ChimeraButtonSize.medium:
        return AppTextStyles.labelMedium;
      case ChimeraButtonSize.large:
        return AppTextStyles.labelLarge;
    }
  }
}

/// Button type enumeration
enum ChimeraButtonType {
  primary,
  secondary,
  outline,
  text,
  danger,
}

/// Button size enumeration
enum ChimeraButtonSize {
  small,
  medium,
  large,
}
