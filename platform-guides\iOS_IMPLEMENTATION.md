# iOS Implementation Guide - Project Chimera

## Overview

This guide details the iOS-specific implementation requirements and strategies for Project Chimera's multi-hop VPN functionality.

## 1. iOS Architecture

### 1.1 App Structure

```
Chimera.app/
├── Main App Target (Flutter)
│   ├── UI Layer (Dart/Flutter)
│   ├── Business Logic (Dart)
│   └── Platform Bridge (Swift)
├── Network Extension Target
│   ├── Packet Tunnel Provider (Swift)
│   ├── DNS Proxy Provider (Swift)
│   └── VPN Protocol Handlers (Swift)
└── Shared Framework
    ├── Configuration Models (Swift)
    ├── Crypto Utilities (Swift)
    └── Network Utilities (Swift)
```

### 1.2 Key iOS Frameworks

```swift
import NetworkExtension
import Network
import CryptoKit
import Security
import SystemConfiguration
import os.log
```

## 2. NetworkExtension Implementation

### 2.1 Packet Tunnel Provider

```swift
import NetworkExtension
import Network

class ChimeraPacketTunnelProvider: NEPacketTunnelProvider {
    private var chainManager: VPNChainManager?
    private var currentChain: VPNChain?
    
    override func startTunnel(options: [String : NSObject]?) async throws {
        // Parse chain configuration from options
        guard let chainConfig = parseChainConfiguration(options) else {
            throw ChimeraError.invalidConfiguration
        }
        
        // Initialize chain manager
        chainManager = VPNChainManager()
        
        // Establish multi-hop chain
        currentChain = try await chainManager?.establishChain(chainConfig)
        
        // Configure tunnel settings
        let tunnelSettings = createTunnelSettings(for: chainConfig)
        try await setTunnelNetworkSettings(tunnelSettings)
        
        // Start packet processing
        startPacketProcessing()
    }
    
    override func stopTunnel(with reason: NEProviderStopReason) async {
        // Gracefully disconnect chain
        await chainManager?.disconnectChain()
        
        // Clean up resources
        cleanup()
    }
    
    private func startPacketProcessing() {
        // Read packets from tunnel interface
        packetFlow.readPackets { [weak self] packets, protocols in
            self?.processPackets(packets, protocols: protocols)
        }
    }
    
    private func processPackets(_ packets: [Data], protocols: [NSNumber]) {
        // Route packets through VPN chain
        Task {
            let processedPackets = await chainManager?.routePackets(packets, protocols: protocols)
            
            // Write processed packets back to tunnel
            if let processedPackets = processedPackets {
                packetFlow.writePackets(processedPackets.packets, 
                                      withProtocols: processedPackets.protocols)
            }
            
            // Continue reading packets
            startPacketProcessing()
        }
    }
}
```

### 2.2 DNS Proxy Provider

```swift
import NetworkExtension

class ChimeraDNSProxyProvider: NEDNSProxyProvider {
    private var dnsProtection: DNSProtectionManager?
    
    override func startProxy(options: [String : Any]?) async throws {
        // Initialize DNS protection
        dnsProtection = DNSProtectionManager()
        
        // Configure trusted DNS servers
        let dnsConfig = parseDNSConfiguration(options)
        try await dnsProtection?.configure(dnsConfig)
        
        // Start DNS interception
        startDNSInterception()
    }
    
    override func stopProxy(with reason: NEProviderStopReason) async {
        await dnsProtection?.stop()
        dnsProtection = nil
    }
    
    private func startDNSInterception() {
        // Handle DNS queries
        handleNewFlow { [weak self] flow in
            if let dnsFlow = flow as? NEDNSProxyFlow {
                self?.handleDNSQuery(dnsFlow)
            }
            return true
        }
    }
    
    private func handleDNSQuery(_ flow: NEDNSProxyFlow) {
        Task {
            // Route DNS query through VPN
            let response = await dnsProtection?.processQuery(flow.dnsQuery)
            
            // Send response back
            if let response = response {
                flow.respond(with: response)
            }
        }
    }
}
```

## 3. VPN Chain Management

### 3.1 Chain Manager Implementation

```swift
class VPNChainManager {
    private var hops: [VPNHop] = []
    private var isConnected = false
    
    func establishChain(_ config: VPNChainConfiguration) async throws -> VPNChain {
        // Validate chain configuration
        try validateChainConfiguration(config)
        
        // Establish hops sequentially
        for (index, hopConfig) in config.hops.enumerated() {
            let hop = try await establishHop(hopConfig, index: index)
            hops.append(hop)
        }
        
        // Configure routing
        try await configureChainRouting()
        
        isConnected = true
        return VPNChain(hops: hops, configuration: config)
    }
    
    private func establishHop(_ config: VPNHopConfiguration, index: Int) async throws -> VPNHop {
        let protocolHandler = createProtocolHandler(config.protocol)
        
        // For first hop, connect directly
        if index == 0 {
            return try await protocolHandler.connect(to: config.server)
        }
        
        // For subsequent hops, route through previous hops
        let routingPath = Array(hops[0..<index])
        return try await protocolHandler.connectThroughPath(to: config.server, 
                                                           via: routingPath)
    }
    
    private func createProtocolHandler(_ protocol: VPNProtocolType) -> VPNProtocolHandler {
        switch protocol {
        case .wireGuard:
            return WireGuardHandler()
        case .openVPN:
            return OpenVPNHandler()
        }
    }
}
```

### 3.2 WireGuard Implementation

```swift
import WireGuardKit

class WireGuardHandler: VPNProtocolHandler {
    private var tunnel: WireGuardAdapter?
    
    func connect(to server: VPNServer) async throws -> VPNHop {
        // Parse WireGuard configuration
        let config = try parseWireGuardConfig(server.configuration)
        
        // Create WireGuard adapter
        tunnel = WireGuardAdapter(with: self)
        
        // Start tunnel
        try await tunnel?.start(tunnelConfiguration: config)
        
        return VPNHop(server: server, protocol: .wireGuard, adapter: tunnel)
    }
    
    func connectThroughPath(to server: VPNServer, via path: [VPNHop]) async throws -> VPNHop {
        // Configure routing through existing hops
        let routedConfig = try configureRoutingThroughPath(server.configuration, path: path)
        
        // Establish connection
        return try await connect(to: VPNServer(server: server, configuration: routedConfig))
    }
    
    private func parseWireGuardConfig(_ configData: Data) throws -> TunnelConfiguration {
        let configString = String(data: configData, encoding: .utf8) ?? ""
        return try TunnelConfiguration(fromWgQuickConfig: configString)
    }
}
```

## 4. Security Implementation

### 4.1 Kill Switch

```swift
import NetworkExtension

class iOSKillSwitch {
    private var vpnManager: NEVPNManager?
    
    func enableKillSwitch() async throws {
        vpnManager = NEVPNManager.shared()
        
        // Configure on-demand rules for kill switch
        let onDemandRule = NEOnDemandRuleConnect()
        onDemandRule.interfaceTypeMatch = .any
        
        // Block all traffic when VPN is not connected
        let blockRule = NEOnDemandRuleDisconnect()
        blockRule.interfaceTypeMatch = .any
        
        vpnManager?.onDemandRules = [onDemandRule, blockRule]
        vpnManager?.isOnDemandEnabled = true
        
        try await vpnManager?.saveToPreferences()
        try await vpnManager?.loadFromPreferences()
    }
    
    func disableKillSwitch() async throws {
        vpnManager?.isOnDemandEnabled = false
        vpnManager?.onDemandRules = []
        
        try await vpnManager?.saveToPreferences()
    }
}
```

### 4.2 Secure Storage

```swift
import Security

class SecureConfigurationStorage {
    private let serviceName = "com.chimera.vpn.config"
    
    func storeChainConfiguration(_ config: VPNChainConfiguration) throws {
        let data = try JSONEncoder().encode(config)
        
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceName,
            kSecAttrAccount as String: config.id,
            kSecValueData as String: data,
            kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        ]
        
        let status = SecItemAdd(query as CFDictionary, nil)
        
        if status == errSecDuplicateItem {
            // Update existing item
            let updateQuery: [String: Any] = [
                kSecClass as String: kSecClassGenericPassword,
                kSecAttrService as String: serviceName,
                kSecAttrAccount as String: config.id
            ]
            
            let updateAttributes: [String: Any] = [
                kSecValueData as String: data
            ]
            
            let updateStatus = SecItemUpdate(updateQuery as CFDictionary, 
                                           updateAttributes as CFDictionary)
            
            if updateStatus != errSecSuccess {
                throw ChimeraError.keychainError(updateStatus)
            }
        } else if status != errSecSuccess {
            throw ChimeraError.keychainError(status)
        }
    }
    
    func loadChainConfiguration(_ id: String) throws -> VPNChainConfiguration? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceName,
            kSecAttrAccount as String: id,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        if status == errSecSuccess, let data = result as? Data {
            return try JSONDecoder().decode(VPNChainConfiguration.self, from: data)
        } else if status == errSecItemNotFound {
            return nil
        } else {
            throw ChimeraError.keychainError(status)
        }
    }
}
```

## 5. Flutter Integration

### 5.1 Method Channel Bridge

```swift
import Flutter

class ChimeraMethodChannel: NSObject, FlutterPlugin {
    private let channelName = "com.chimera.vpn/native"
    private var vpnManager: ChimeraVPNManager?
    
    static func register(with registrar: FlutterPluginRegistrar) {
        let channel = FlutterMethodChannel(name: "com.chimera.vpn/native", 
                                         binaryMessenger: registrar.messenger())
        let instance = ChimeraMethodChannel()
        registrar.addMethodCallDelegate(instance, channel: channel)
    }
    
    func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case "connectChain":
            handleConnectChain(call, result: result)
        case "disconnectChain":
            handleDisconnectChain(call, result: result)
        case "getConnectionStatus":
            handleGetConnectionStatus(call, result: result)
        case "enableKillSwitch":
            handleEnableKillSwitch(call, result: result)
        default:
            result(FlutterMethodNotImplemented)
        }
    }
    
    private func handleConnectChain(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let chainConfigData = args["chainConfig"] as? [String: Any] else {
            result(FlutterError(code: "INVALID_ARGUMENTS", 
                              message: "Chain configuration required", 
                              details: nil))
            return
        }
        
        Task {
            do {
                let chainConfig = try parseChainConfiguration(chainConfigData)
                try await vpnManager?.connectChain(chainConfig)
                result(true)
            } catch {
                result(FlutterError(code: "CONNECTION_FAILED", 
                                  message: error.localizedDescription, 
                                  details: nil))
            }
        }
    }
}
```

## 6. App Store Considerations

### 6.1 Required Entitlements

```xml
<!-- Entitlements.plist -->
<key>com.apple.developer.networking.networkextension</key>
<array>
    <string>packet-tunnel-provider</string>
    <string>dns-proxy</string>
</array>

<key>com.apple.security.application-groups</key>
<array>
    <string>group.com.chimera.vpn</string>
</array>

<key>com.apple.developer.networking.vpn.api</key>
<array>
    <string>allow-vpn</string>
</array>
```

### 6.2 App Store Review Guidelines

**Key Considerations**:
- VPN apps must use NEVPNManager or NetworkExtension
- Clear privacy policy required
- No circumvention of parental controls
- Compliance with local laws and regulations
- Proper handling of user data

**Review Preparation**:
- Detailed app description
- Privacy policy link
- Demo account for reviewers
- Clear explanation of VPN functionality
- Compliance documentation

## 7. Testing Strategy

### 7.1 Unit Testing

```swift
import XCTest
@testable import ChimeraVPN

class VPNChainManagerTests: XCTestCase {
    var chainManager: VPNChainManager!
    
    override func setUp() {
        super.setUp()
        chainManager = VPNChainManager()
    }
    
    func testChainEstablishment() async throws {
        let config = createTestChainConfiguration()
        let chain = try await chainManager.establishChain(config)
        
        XCTAssertEqual(chain.hops.count, config.hops.count)
        XCTAssertTrue(chainManager.isConnected)
    }
    
    func testInvalidChainConfiguration() async {
        let invalidConfig = VPNChainConfiguration(hops: [])
        
        do {
            _ = try await chainManager.establishChain(invalidConfig)
            XCTFail("Should throw error for empty chain")
        } catch ChimeraError.invalidConfiguration {
            // Expected error
        } catch {
            XCTFail("Unexpected error: \(error)")
        }
    }
}
```

### 7.2 Integration Testing

```swift
class NetworkExtensionIntegrationTests: XCTestCase {
    func testPacketTunnelProvider() async throws {
        let provider = ChimeraPacketTunnelProvider()
        let options = createTestOptions()
        
        try await provider.startTunnel(options: options)
        
        // Verify tunnel is established
        XCTAssertNotNil(provider.tunnelConfiguration)
        
        await provider.stopTunnel(with: .userInitiated)
    }
}
```

This iOS implementation guide provides the foundation for building Project Chimera's multi-hop VPN functionality on iOS while ensuring App Store compliance and optimal security.
