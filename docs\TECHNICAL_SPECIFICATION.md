# Project Chimera - Technical Specification

## Executive Summary

Project Chimera is a revolutionary VPN application that empowers users to create custom, multi-hop security chains using various protocols and endpoints. Unlike traditional VPNs that connect to a single server, Chimera allows users to construct dynamic connection paths through multiple servers, each potentially using different protocols, with optional obfuscation and Tor integration.

## 1. Core Architecture - Multi-Hop Chain System

### 1.1 Connection Chain Model

The fundamental architecture revolves around a **Connection Chain** - a sequence of VPN hops that traffic traverses before reaching the internet:

```
[User Device] → [Hop 1] → [Hop 2] → [Hop 3] → [Internet]
```

Each hop represents:
- A VPN server endpoint
- A specific protocol (WireGuard, OpenVPN UDP/TCP)
- Optional obfuscation layer (obfsproxy, stunnel)
- Configuration parameters (encryption, authentication)

### 1.2 Chain Management System

#### Chain Definition Structure
```json
{
  "chainId": "uuid",
  "name": "My Secure Chain",
  "description": "High-security chain through EU servers",
  "hops": [
    {
      "hopId": "uuid",
      "order": 1,
      "server": {
        "id": "server-uuid",
        "name": "Amsterdam-WG-01",
        "endpoint": "vpn.example.com:51820",
        "location": {"country": "NL", "city": "Amsterdam"},
        "source": "user_added|community",
        "trustLevel": "trusted|untrusted"
      },
      "protocol": {
        "type": "wireguard",
        "config": {
          "privateKey": "encrypted",
          "publicKey": "...",
          "allowedIPs": "0.0.0.0/0"
        }
      },
      "obfuscation": {
        "enabled": false,
        "type": null
      }
    }
  ],
  "torExit": false,
  "metadata": {
    "created": "timestamp",
    "lastUsed": "timestamp",
    "totalConnections": 0
  }
}
```

#### Chain Execution Engine

The Chain Execution Engine manages the sequential establishment and maintenance of VPN connections:

1. **Connection Establishment Phase**
   - Validate chain configuration
   - Establish first hop connection
   - Route traffic through first hop to establish second hop
   - Continue sequentially until all hops are active
   - Optionally establish Tor connection as final hop

2. **Traffic Routing Phase**
   - Implement traffic routing rules
   - Monitor connection health for each hop
   - Handle protocol-specific requirements
   - Manage encryption/decryption at each layer

3. **Failure Recovery Phase**
   - Detect hop failures
   - Implement reconnection strategies
   - Maintain kill switch during recovery
   - Log failure events for analysis

### 1.3 Protocol Abstraction Layer

A unified interface abstracts different VPN protocols:

```typescript
interface VPNProtocol {
  connect(config: ProtocolConfig): Promise<Connection>;
  disconnect(): Promise<void>;
  getStatus(): ConnectionStatus;
  getStatistics(): ConnectionStats;
  supportsObfuscation(): boolean;
}

class WireGuardProtocol implements VPNProtocol { ... }
class OpenVPNProtocol implements VPNProtocol { ... }
```

### 1.4 Obfuscation Integration

#### Supported Obfuscation Methods

1. **obfsproxy (obfs4)**
   - Disguises VPN traffic as HTTPS
   - Integrated as first hop in chain
   - Configurable bridge endpoints

2. **stunnel**
   - Wraps VPN protocols in TLS/SSL
   - Particularly effective with OpenVPN
   - Custom certificate management

#### Implementation Strategy
- Obfuscation runs as separate process/service
- VPN traffic routed through obfuscation proxy
- Transparent to upper protocol layers
- Platform-specific implementations for optimal performance

### 1.5 Tor Integration

Tor integration provides ultimate anonymity as the final exit point:

- Embedded Tor client for mobile platforms
- System Tor integration for desktop platforms
- Configurable exit node preferences
- Bridge support for censored networks

## 2. Server Sourcing & Management

### 2.1 Server Source Categories

#### User-Added Custom Servers (Primary Method)
- **File Import**: Support for .conf (WireGuard) and .ovpn (OpenVPN) files
- **QR Code Import**: Camera-based configuration import
- **Manual Entry**: Form-based server configuration
- **Provider Integration**: API integration with major VPN providers

#### Community-Sourced Servers (Secondary Method)
- Integration with VPN Gate Academic Experiment Project
- Public server directories with community validation
- **Critical Security Warnings**: Persistent, non-dismissible warnings about risks

### 2.2 Server Metadata System

Each server maintains comprehensive metadata:

```json
{
  "serverId": "uuid",
  "name": "Amsterdam-01",
  "endpoint": "vpn.example.com:51820",
  "location": {
    "country": "NL",
    "countryName": "Netherlands",
    "city": "Amsterdam",
    "coordinates": [52.3676, 4.9041]
  },
  "protocols": ["wireguard", "openvpn_udp", "openvpn_tcp"],
  "source": "user_added|community|provider",
  "trustLevel": "trusted|untrusted|unknown",
  "performance": {
    "uptime": 99.5,
    "latency": 45,
    "bandwidth": "1Gbps",
    "load": 0.3,
    "lastChecked": "timestamp"
  },
  "security": {
    "loggingPolicy": "no_logs|unknown|logs_metadata|logs_traffic",
    "jurisdiction": "NL",
    "encryption": "AES-256-GCM",
    "authentication": "RSA-4096"
  },
  "reputation": {
    "communityScore": 4.2,
    "reportCount": 0,
    "verificationStatus": "verified|unverified|flagged"
  }
}
```

### 2.3 Server Validation & Monitoring

#### Real-time Health Monitoring
- Continuous latency and bandwidth testing
- Uptime monitoring with historical data
- Connection success rate tracking
- Geographic verification

#### Security Validation
- Certificate validation for TLS-based protocols
- DNS leak testing
- IPv6 leak detection
- Traffic analysis resistance testing

#### Community Reporting System
- User-driven server quality reporting
- Automated malicious activity detection
- Reputation scoring algorithm
- Blacklist management for compromised servers

## 3. Advanced Security Framework

### 3.1 Kill Switch Implementation

#### System-Level Kill Switch (Default)
- **Firewall-based blocking**: All traffic blocked if VPN disconnects
- **IPv4 and IPv6 protection**: Comprehensive IP version coverage
- **DNS blocking**: Prevent DNS leaks during disconnection
- **Platform-specific implementation**:
  - Windows: WinDivert or WFP (Windows Filtering Platform)
  - macOS: pfctl firewall rules
  - Linux: iptables/netfilter rules
  - iOS: NEVPNManager with on-demand rules
  - Android: VpnService with PROTECT_FROM_VPN

#### Application-Level Kill Switch (Optional)
- Selective application termination
- Process monitoring and control
- User-defined application lists
- Graceful vs. forced termination options

### 3.2 DNS Leak Protection

#### DNS Routing Strategy
- Force all DNS queries through VPN tunnel
- Support for DNS over HTTPS (DoH) and DNS over TLS (DoT)
- Custom DNS provider selection
- DNS query logging prevention

#### Trusted DNS Providers
- Quad9 (*******) - Security-focused
- Cloudflare (*******) - Privacy-focused
- Custom DNS servers
- Provider-specific DNS servers

### 3.3 IPv6 Leak Protection

- **IPv6 traffic blocking**: Disable IPv6 on VPN-only mode
- **IPv6 tunneling**: Route IPv6 through IPv4 VPN tunnels
- **Dual-stack support**: Native IPv6 VPN support where available
- **Leak detection**: Continuous monitoring for IPv6 leaks

### 3.4 Traffic Analysis Resistance

#### Protocol Obfuscation
- Traffic pattern randomization
- Packet size padding
- Timing attack mitigation
- Deep packet inspection evasion

#### Metadata Protection
- Connection timing obfuscation
- Bandwidth usage patterns
- Protocol fingerprint masking
- Geographic location masking

## 4. Cross-Platform Architecture

### 4.1 Technology Stack

#### Frontend Framework
**Primary Recommendation: Flutter**
- Single codebase for all platforms
- Native performance
- Rich UI components
- Strong community support
- Google backing ensures longevity

**Alternative: React Native**
- JavaScript ecosystem
- Hot reload development
- Large developer community
- Facebook/Meta backing

#### Backend Services (Optional)
- **User Account Sync**: Firebase or custom backend
- **Server Directory**: RESTful API for community servers
- **Analytics**: Privacy-preserving usage analytics
- **Update Service**: Automatic application updates

### 4.2 Platform-Specific Implementations

#### iOS Implementation
- **VPN Framework**: NetworkExtension (NEVPNManager, NEPacketTunnelProvider)
- **Kill Switch**: On-demand VPN rules
- **DNS Protection**: NEDNSProxyProvider
- **App Store Compliance**: Careful feature implementation for approval

#### Android Implementation
- **VPN Framework**: VpnService API
- **Kill Switch**: PROTECT_FROM_VPN flag
- **DNS Protection**: Custom DNS resolver
- **Background Processing**: Foreground service for VPN

#### Windows Implementation
- **VPN Framework**: WinTun/WinDivert for packet capture
- **Kill Switch**: Windows Filtering Platform (WFP)
- **DNS Protection**: System DNS override
- **Service Architecture**: Windows service for background operation

#### macOS Implementation
- **VPN Framework**: NetworkExtension (similar to iOS)
- **Kill Switch**: pfctl firewall rules
- **DNS Protection**: System DNS configuration
- **Sandboxing**: App Store sandbox compatibility

#### Linux Implementation
- **VPN Framework**: TUN/TAP interfaces
- **Kill Switch**: iptables/netfilter rules
- **DNS Protection**: resolv.conf management
- **Distribution**: AppImage, Snap, or native packages

### 4.3 Native Bridge Architecture

```
┌─────────────────────────────────────┐
│           Flutter/RN UI             │
├─────────────────────────────────────┤
│         Shared Business Logic       │
├─────────────────────────────────────┤
│        Platform Bridge Layer       │
├─────────────────────────────────────┤
│     Platform-Specific VPN Core     │
└─────────────────────────────────────┘
```

The bridge layer handles:
- VPN protocol implementations
- System-level security features
- Platform-specific APIs
- Performance-critical operations

## 5. Data Flow Architecture

### 5.1 Multi-Hop Connection Flow

```
User Application
    ↓ (1. Initiate Connection)
Chain Manager
    ↓ (2. Validate Chain)
Connection Orchestrator
    ↓ (3. Establish Hop 1)
Protocol Handler (WireGuard/OpenVPN)
    ↓ (4. Route through Hop 1 to establish Hop 2)
Protocol Handler (Next Hop)
    ↓ (5. Continue until all hops established)
Traffic Router
    ↓ (6. Route user traffic through chain)
Internet
```

### 5.2 Security Layer Integration

```
User Traffic
    ↓
Kill Switch Monitor ←→ Connection Health Monitor
    ↓
DNS Leak Protection
    ↓
IPv6 Leak Protection
    ↓
Split Tunneling Filter
    ↓
VPN Chain (Hop 1 → Hop 2 → ... → Hop N)
    ↓
Optional Tor Exit
    ↓
Internet
```

This technical specification provides the foundation for implementing Project Chimera. The next sections will detail the UI/UX design, implementation roadmap, and platform-specific considerations.
