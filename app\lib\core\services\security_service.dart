import 'dart:async';
import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:process_run/process_run.dart';
import 'package:dio/dio.dart';

/// Security service for VPN protection features
class SecurityService {
  static const String _tag = 'SecurityService';
  
  final Dio _dio = Dio();
  Timer? _leakTestTimer;
  Timer? _killSwitchMonitor;
  
  bool _killSwitchEnabled = false;
  bool _dnsProtectionEnabled = false;
  bool _ipv6ProtectionEnabled = false;
  
  final StreamController<SecurityStatus> _statusController = 
      StreamController<SecurityStatus>.broadcast();
  
  Stream<SecurityStatus> get statusStream => _statusController.stream;
  
  /// Initialize security service
  Future<void> initialize() async {
    debugPrint('$_tag: Initializing security service');
    
    // Start periodic security checks
    _startSecurityMonitoring();
  }
  
  /// Enable kill switch protection
  Future<bool> enableKillSwitch() async {
    try {
      debugPrint('$_tag: Enabling kill switch');
      
      if (Platform.isWindows) {
        return await _enableKillSwitchWindows();
      } else if (Platform.isMacOS) {
        return await _enableKillSwitchMacOS();
      } else if (Platform.isLinux) {
        return await _enableKillSwitchLinux();
      }
      
      return false;
    } catch (e) {
      debugPrint('$_tag: Kill switch enable failed: $e');
      return false;
    }
  }
  
  /// Disable kill switch protection
  Future<bool> disableKillSwitch() async {
    try {
      debugPrint('$_tag: Disabling kill switch');
      
      if (Platform.isWindows) {
        return await _disableKillSwitchWindows();
      } else if (Platform.isMacOS) {
        return await _disableKillSwitchMacOS();
      } else if (Platform.isLinux) {
        return await _disableKillSwitchLinux();
      }
      
      return false;
    } catch (e) {
      debugPrint('$_tag: Kill switch disable failed: $e');
      return false;
    }
  }
  
  /// Enable DNS leak protection
  Future<bool> enableDNSProtection(List<String> dnsServers) async {
    try {
      debugPrint('$_tag: Enabling DNS protection with servers: $dnsServers');
      
      if (Platform.isWindows) {
        return await _enableDNSProtectionWindows(dnsServers);
      } else if (Platform.isMacOS) {
        return await _enableDNSProtectionMacOS(dnsServers);
      } else if (Platform.isLinux) {
        return await _enableDNSProtectionLinux(dnsServers);
      }
      
      return false;
    } catch (e) {
      debugPrint('$_tag: DNS protection enable failed: $e');
      return false;
    }
  }
  
  /// Disable DNS leak protection
  Future<bool> disableDNSProtection() async {
    try {
      debugPrint('$_tag: Disabling DNS protection');
      
      if (Platform.isWindows) {
        return await _disableDNSProtectionWindows();
      } else if (Platform.isMacOS) {
        return await _disableDNSProtectionMacOS();
      } else if (Platform.isLinux) {
        return await _disableDNSProtectionLinux();
      }
      
      return false;
    } catch (e) {
      debugPrint('$_tag: DNS protection disable failed: $e');
      return false;
    }
  }
  
  /// Enable IPv6 protection (block IPv6 traffic)
  Future<bool> enableIPv6Protection() async {
    try {
      debugPrint('$_tag: Enabling IPv6 protection');
      
      if (Platform.isWindows) {
        return await _enableIPv6ProtectionWindows();
      } else if (Platform.isMacOS) {
        return await _enableIPv6ProtectionMacOS();
      } else if (Platform.isLinux) {
        return await _enableIPv6ProtectionLinux();
      }
      
      return false;
    } catch (e) {
      debugPrint('$_tag: IPv6 protection enable failed: $e');
      return false;
    }
  }
  
  /// Disable IPv6 protection
  Future<bool> disableIPv6Protection() async {
    try {
      debugPrint('$_tag: Disabling IPv6 protection');
      
      if (Platform.isWindows) {
        return await _disableIPv6ProtectionWindows();
      } else if (Platform.isMacOS) {
        return await _disableIPv6ProtectionMacOS();
      } else if (Platform.isLinux) {
        return await _disableIPv6ProtectionLinux();
      }
      
      return false;
    } catch (e) {
      debugPrint('$_tag: IPv6 protection disable failed: $e');
      return false;
    }
  }
  
  /// Perform DNS leak test
  Future<DNSLeakTestResult> performDNSLeakTest() async {
    try {
      debugPrint('$_tag: Performing DNS leak test');
      
      final testDomains = [
        'test1.dnsleaktest.com',
        'test2.dnsleaktest.com', 
        'test3.dnsleaktest.com',
      ];
      
      final results = <DNSQueryResult>[];
      
      for (final domain in testDomains) {
        try {
          final addresses = await InternetAddress.lookup(domain);
          if (addresses.isNotEmpty) {
            results.add(DNSQueryResult(
              domain: domain,
              resolvedIP: addresses.first.address,
              timestamp: DateTime.now(),
            ));
          }
        } catch (e) {
          debugPrint('$_tag: DNS lookup failed for $domain: $e');
        }
      }
      
      // Analyze results for leaks
      final hasLeak = await _analyzeDNSResults(results);
      
      return DNSLeakTestResult(
        hasLeak: hasLeak,
        results: results,
        timestamp: DateTime.now(),
      );
    } catch (e) {
      debugPrint('$_tag: DNS leak test failed: $e');
      return DNSLeakTestResult(
        hasLeak: true,
        results: [],
        timestamp: DateTime.now(),
        error: e.toString(),
      );
    }
  }
  
  /// Perform IPv6 leak test
  Future<IPv6LeakTestResult> performIPv6LeakTest() async {
    try {
      debugPrint('$_tag: Performing IPv6 leak test');
      
      // Try to connect to IPv6 test sites
      final testSites = [
        'ipv6.google.com',
        'test-ipv6.com',
        'ipv6-test.com',
      ];
      
      bool hasIPv6Leak = false;
      final results = <String>[];
      
      for (final site in testSites) {
        try {
          final addresses = await InternetAddress.lookup(site, type: InternetAddressType.IPv6);
          if (addresses.isNotEmpty) {
            hasIPv6Leak = true;
            results.add('IPv6 connection possible to $site');
          }
        } catch (e) {
          // IPv6 blocked/unavailable - this is good for protection
          results.add('IPv6 blocked for $site');
        }
      }
      
      return IPv6LeakTestResult(
        hasLeak: hasIPv6Leak,
        results: results,
        timestamp: DateTime.now(),
      );
    } catch (e) {
      debugPrint('$_tag: IPv6 leak test failed: $e');
      return IPv6LeakTestResult(
        hasLeak: false,
        results: [],
        timestamp: DateTime.now(),
        error: e.toString(),
      );
    }
  }
  
  // Windows-specific implementations
  
  Future<bool> _enableKillSwitchWindows() async {
    try {
      // Use Windows Firewall to block all traffic except VPN
      final commands = [
        'netsh advfirewall set allprofiles firewallpolicy blockinbound,blockoutbound',
        'netsh advfirewall firewall add rule name="ChimeraVPN-Allow-VPN" dir=out action=allow protocol=any remoteip=10.0.0.0/8',
        'netsh advfirewall firewall add rule name="ChimeraVPN-Allow-Local" dir=out action=allow protocol=any remoteip=***********/16',
        'netsh advfirewall firewall add rule name="ChimeraVPN-Allow-DNS" dir=out action=allow protocol=udp remoteport=53',
      ];
      
      for (final command in commands) {
        final result = await Process.run('cmd', ['/c', command]);
        if (result.exitCode != 0) {
          debugPrint('$_tag: Kill switch command failed: ${result.stderr}');
          return false;
        }
      }
      
      _killSwitchEnabled = true;
      _startKillSwitchMonitoring();
      return true;
    } catch (e) {
      debugPrint('$_tag: Windows kill switch enable error: $e');
      return false;
    }
  }
  
  Future<bool> _disableKillSwitchWindows() async {
    try {
      // Remove firewall rules
      final commands = [
        'netsh advfirewall firewall delete rule name="ChimeraVPN-Allow-VPN"',
        'netsh advfirewall firewall delete rule name="ChimeraVPN-Allow-Local"',
        'netsh advfirewall firewall delete rule name="ChimeraVPN-Allow-DNS"',
        'netsh advfirewall set allprofiles firewallpolicy blockinbound,allowoutbound',
      ];
      
      for (final command in commands) {
        await Process.run('cmd', ['/c', command]);
      }
      
      _killSwitchEnabled = false;
      _killSwitchMonitor?.cancel();
      return true;
    } catch (e) {
      debugPrint('$_tag: Windows kill switch disable error: $e');
      return false;
    }
  }
  
  Future<bool> _enableDNSProtectionWindows(List<String> dnsServers) async {
    try {
      // Set DNS servers for all network interfaces
      final interfaces = await _getNetworkInterfaces();
      
      for (final interface in interfaces) {
        final dnsString = dnsServers.join(',');
        final result = await Process.run('netsh', [
          'interface', 'ip', 'set', 'dns', 
          'name="$interface"', 
          'static', 
          dnsServers.first
        ]);
        
        if (result.exitCode == 0 && dnsServers.length > 1) {
          // Add secondary DNS
          await Process.run('netsh', [
            'interface', 'ip', 'add', 'dns',
            'name="$interface"',
            dnsServers[1],
            'index=2'
          ]);
        }
      }
      
      _dnsProtectionEnabled = true;
      return true;
    } catch (e) {
      debugPrint('$_tag: Windows DNS protection enable error: $e');
      return false;
    }
  }
  
  Future<bool> _disableDNSProtectionWindows() async {
    try {
      // Reset DNS to automatic for all interfaces
      final interfaces = await _getNetworkInterfaces();
      
      for (final interface in interfaces) {
        await Process.run('netsh', [
          'interface', 'ip', 'set', 'dns',
          'name="$interface"',
          'dhcp'
        ]);
      }
      
      _dnsProtectionEnabled = false;
      return true;
    } catch (e) {
      debugPrint('$_tag: Windows DNS protection disable error: $e');
      return false;
    }
  }
  
  Future<bool> _enableIPv6ProtectionWindows() async {
    try {
      // Disable IPv6 on all network interfaces
      final result = await Process.run('netsh', [
        'interface', 'ipv6', 'set', 'global', 'randomizeidentifiers=disabled'
      ]);
      
      if (result.exitCode == 0) {
        // Add firewall rule to block IPv6
        await Process.run('netsh', [
          'advfirewall', 'firewall', 'add', 'rule',
          'name="ChimeraVPN-Block-IPv6"',
          'dir=out',
          'action=block',
          'protocol=ipv6'
        ]);
        
        _ipv6ProtectionEnabled = true;
        return true;
      }
      
      return false;
    } catch (e) {
      debugPrint('$_tag: Windows IPv6 protection enable error: $e');
      return false;
    }
  }
  
  Future<bool> _disableIPv6ProtectionWindows() async {
    try {
      // Remove IPv6 blocking rule
      await Process.run('netsh', [
        'advfirewall', 'firewall', 'delete', 'rule',
        'name="ChimeraVPN-Block-IPv6"'
      ]);
      
      // Re-enable IPv6
      await Process.run('netsh', [
        'interface', 'ipv6', 'set', 'global', 'randomizeidentifiers=enabled'
      ]);
      
      _ipv6ProtectionEnabled = false;
      return true;
    } catch (e) {
      debugPrint('$_tag: Windows IPv6 protection disable error: $e');
      return false;
    }
  }
  
  // macOS and Linux implementations (placeholder)
  
  Future<bool> _enableKillSwitchMacOS() async {
    debugPrint('$_tag: macOS kill switch not yet implemented');
    return false;
  }
  
  Future<bool> _disableKillSwitchMacOS() async {
    debugPrint('$_tag: macOS kill switch disable not yet implemented');
    return false;
  }
  
  Future<bool> _enableDNSProtectionMacOS(List<String> dnsServers) async {
    debugPrint('$_tag: macOS DNS protection not yet implemented');
    return false;
  }
  
  Future<bool> _disableDNSProtectionMacOS() async {
    debugPrint('$_tag: macOS DNS protection disable not yet implemented');
    return false;
  }
  
  Future<bool> _enableIPv6ProtectionMacOS() async {
    debugPrint('$_tag: macOS IPv6 protection not yet implemented');
    return false;
  }
  
  Future<bool> _disableIPv6ProtectionMacOS() async {
    debugPrint('$_tag: macOS IPv6 protection disable not yet implemented');
    return false;
  }
  
  Future<bool> _enableKillSwitchLinux() async {
    debugPrint('$_tag: Linux kill switch not yet implemented');
    return false;
  }
  
  Future<bool> _disableKillSwitchLinux() async {
    debugPrint('$_tag: Linux kill switch disable not yet implemented');
    return false;
  }
  
  Future<bool> _enableDNSProtectionLinux(List<String> dnsServers) async {
    debugPrint('$_tag: Linux DNS protection not yet implemented');
    return false;
  }
  
  Future<bool> _disableDNSProtectionLinux() async {
    debugPrint('$_tag: Linux DNS protection disable not yet implemented');
    return false;
  }
  
  Future<bool> _enableIPv6ProtectionLinux() async {
    debugPrint('$_tag: Linux IPv6 protection not yet implemented');
    return false;
  }
  
  Future<bool> _disableIPv6ProtectionLinux() async {
    debugPrint('$_tag: Linux IPv6 protection disable not yet implemented');
    return false;
  }
  
  // Helper methods
  
  Future<List<String>> _getNetworkInterfaces() async {
    try {
      final result = await Process.run('wmic', [
        'path', 'win32_networkadapter', 'where', 'NetEnabled=true',
        'get', 'NetConnectionID', '/format:csv'
      ]);
      
      final output = result.stdout.toString();
      final interfaces = <String>[];
      
      for (final line in output.split('\n')) {
        if (line.contains(',') && !line.contains('NetConnectionID')) {
          final parts = line.split(',');
          if (parts.length > 1 && parts[1].trim().isNotEmpty) {
            interfaces.add(parts[1].trim());
          }
        }
      }
      
      return interfaces;
    } catch (e) {
      debugPrint('$_tag: Failed to get network interfaces: $e');
      return [];
    }
  }
  
  Future<bool> _analyzeDNSResults(List<DNSQueryResult> results) async {
    // In a real implementation, this would check if DNS queries
    // are going through the VPN or leaking to ISP DNS
    // For now, we'll simulate the check
    return results.length < 3; // Simulate leak if not all queries succeeded
  }
  
  void _startSecurityMonitoring() {
    _leakTestTimer = Timer.periodic(const Duration(minutes: 5), (timer) async {
      final dnsResult = await performDNSLeakTest();
      final ipv6Result = await performIPv6LeakTest();
      
      _statusController.add(SecurityStatus(
        killSwitchEnabled: _killSwitchEnabled,
        dnsProtectionEnabled: _dnsProtectionEnabled,
        ipv6ProtectionEnabled: _ipv6ProtectionEnabled,
        dnsLeakDetected: dnsResult.hasLeak,
        ipv6LeakDetected: ipv6Result.hasLeak,
        lastChecked: DateTime.now(),
      ));
    });
  }
  
  void _startKillSwitchMonitoring() {
    _killSwitchMonitor = Timer.periodic(const Duration(seconds: 30), (timer) async {
      // Monitor kill switch status and re-enable if needed
      if (_killSwitchEnabled) {
        // Check if firewall rules are still active
        // Re-apply if necessary
      }
    });
  }
  
  void dispose() {
    _leakTestTimer?.cancel();
    _killSwitchMonitor?.cancel();
    _statusController.close();
  }
}

// Data classes for security results

class SecurityStatus {
  final bool killSwitchEnabled;
  final bool dnsProtectionEnabled;
  final bool ipv6ProtectionEnabled;
  final bool dnsLeakDetected;
  final bool ipv6LeakDetected;
  final DateTime lastChecked;
  
  SecurityStatus({
    required this.killSwitchEnabled,
    required this.dnsProtectionEnabled,
    required this.ipv6ProtectionEnabled,
    required this.dnsLeakDetected,
    required this.ipv6LeakDetected,
    required this.lastChecked,
  });
}

class DNSLeakTestResult {
  final bool hasLeak;
  final List<DNSQueryResult> results;
  final DateTime timestamp;
  final String? error;
  
  DNSLeakTestResult({
    required this.hasLeak,
    required this.results,
    required this.timestamp,
    this.error,
  });
}

class DNSQueryResult {
  final String domain;
  final String resolvedIP;
  final DateTime timestamp;
  
  DNSQueryResult({
    required this.domain,
    required this.resolvedIP,
    required this.timestamp,
  });
}

class IPv6LeakTestResult {
  final bool hasLeak;
  final List<String> results;
  final DateTime timestamp;
  final String? error;
  
  IPv6LeakTestResult({
    required this.hasLeak,
    required this.results,
    required this.timestamp,
    this.error,
  });
}
