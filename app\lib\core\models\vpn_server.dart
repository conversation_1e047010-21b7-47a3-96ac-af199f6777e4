import 'package:hive/hive.dart';

part 'vpn_server.g.dart';

/// VPN server model with connection details and metadata
@HiveType(typeId: 0)
class VPNServer extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String hostname;

  @HiveField(3)
  final int port;

  @HiveField(4)
  final VPNProtocol protocol;

  @HiveField(5)
  final ServerLocation location;

  @HiveField(6)
  final TrustLevel trustLevel;

  @HiveField(7)
  final String? configData;

  @HiveField(8)
  final ServerCredentials? credentials;

  @HiveField(9)
  final DateTime createdAt;

  @HiveField(10)
  final DateTime? lastConnected;

  @HiveField(11)
  final ServerPerformance? performance;

  @HiveField(12)
  final bool isActive;

  VPNServer({
    required this.id,
    required this.name,
    required this.hostname,
    required this.port,
    required this.protocol,
    required this.location,
    required this.trustLevel,
    this.configData,
    this.credentials,
    required this.createdAt,
    this.lastConnected,
    this.performance,
    this.isActive = true,
  });

  VPNServer copyWith({
    String? id,
    String? name,
    String? hostname,
    int? port,
    VPNProtocol? protocol,
    ServerLocation? location,
    TrustLevel? trustLevel,
    String? configData,
    ServerCredentials? credentials,
    DateTime? createdAt,
    DateTime? lastConnected,
    ServerPerformance? performance,
    bool? isActive,
  }) {
    return VPNServer(
      id: id ?? this.id,
      name: name ?? this.name,
      hostname: hostname ?? this.hostname,
      port: port ?? this.port,
      protocol: protocol ?? this.protocol,
      location: location ?? this.location,
      trustLevel: trustLevel ?? this.trustLevel,
      configData: configData ?? this.configData,
      credentials: credentials ?? this.credentials,
      createdAt: createdAt ?? this.createdAt,
      lastConnected: lastConnected ?? this.lastConnected,
      performance: performance ?? this.performance,
      isActive: isActive ?? this.isActive,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'hostname': hostname,
      'port': port,
      'protocol': protocol.name,
      'location': location.toJson(),
      'trustLevel': trustLevel.name,
      'configData': configData,
      'credentials': credentials?.toJson(),
      'createdAt': createdAt.toIso8601String(),
      'lastConnected': lastConnected?.toIso8601String(),
      'performance': performance?.toJson(),
      'isActive': isActive,
    };
  }

  factory VPNServer.fromJson(Map<String, dynamic> json) {
    return VPNServer(
      id: json['id'],
      name: json['name'],
      hostname: json['hostname'],
      port: json['port'],
      protocol: VPNProtocol.values.firstWhere((e) => e.name == json['protocol']),
      location: ServerLocation.fromJson(json['location']),
      trustLevel: TrustLevel.values.firstWhere((e) => e.name == json['trustLevel']),
      configData: json['configData'],
      credentials: json['credentials'] != null 
          ? ServerCredentials.fromJson(json['credentials']) 
          : null,
      createdAt: DateTime.parse(json['createdAt']),
      lastConnected: json['lastConnected'] != null 
          ? DateTime.parse(json['lastConnected']) 
          : null,
      performance: json['performance'] != null 
          ? ServerPerformance.fromJson(json['performance']) 
          : null,
      isActive: json['isActive'] ?? true,
    );
  }
}

@HiveType(typeId: 1)
enum VPNProtocol {
  @HiveField(0)
  wireGuard,
  @HiveField(1)
  openVpnUdp,
  @HiveField(2)
  openVpnTcp,
}

@HiveType(typeId: 2)
enum TrustLevel {
  @HiveField(0)
  trusted,
  @HiveField(1)
  communityVerified,
  @HiveField(2)
  untrusted,
  @HiveField(3)
  flagged,
}

@HiveType(typeId: 3)
class ServerLocation extends HiveObject {
  @HiveField(0)
  final String country;

  @HiveField(1)
  final String countryCode;

  @HiveField(2)
  final String city;

  @HiveField(3)
  final double? latitude;

  @HiveField(4)
  final double? longitude;

  ServerLocation({
    required this.country,
    required this.countryCode,
    required this.city,
    this.latitude,
    this.longitude,
  });

  Map<String, dynamic> toJson() {
    return {
      'country': country,
      'countryCode': countryCode,
      'city': city,
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  factory ServerLocation.fromJson(Map<String, dynamic> json) {
    return ServerLocation(
      country: json['country'],
      countryCode: json['countryCode'],
      city: json['city'],
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
    );
  }
}

@HiveType(typeId: 4)
class ServerCredentials extends HiveObject {
  @HiveField(0)
  final String? username;

  @HiveField(1)
  final String? password;

  @HiveField(2)
  final String? privateKey;

  @HiveField(3)
  final String? publicKey;

  @HiveField(4)
  final String? certificate;

  ServerCredentials({
    this.username,
    this.password,
    this.privateKey,
    this.publicKey,
    this.certificate,
  });

  Map<String, dynamic> toJson() {
    return {
      'username': username,
      'password': password,
      'privateKey': privateKey,
      'publicKey': publicKey,
      'certificate': certificate,
    };
  }

  factory ServerCredentials.fromJson(Map<String, dynamic> json) {
    return ServerCredentials(
      username: json['username'],
      password: json['password'],
      privateKey: json['privateKey'],
      publicKey: json['publicKey'],
      certificate: json['certificate'],
    );
  }
}

@HiveType(typeId: 5)
class ServerPerformance extends HiveObject {
  @HiveField(0)
  final int latency;

  @HiveField(1)
  final double uploadSpeed;

  @HiveField(2)
  final double downloadSpeed;

  @HiveField(3)
  final double uptime;

  @HiveField(4)
  final DateTime lastTested;

  ServerPerformance({
    required this.latency,
    required this.uploadSpeed,
    required this.downloadSpeed,
    required this.uptime,
    required this.lastTested,
  });

  Map<String, dynamic> toJson() {
    return {
      'latency': latency,
      'uploadSpeed': uploadSpeed,
      'downloadSpeed': downloadSpeed,
      'uptime': uptime,
      'lastTested': lastTested.toIso8601String(),
    };
  }

  factory ServerPerformance.fromJson(Map<String, dynamic> json) {
    return ServerPerformance(
      latency: json['latency'],
      uploadSpeed: json['uploadSpeed'].toDouble(),
      downloadSpeed: json['downloadSpeed'].toDouble(),
      uptime: json['uptime'].toDouble(),
      lastTested: DateTime.parse(json['lastTested']),
    );
  }
}
