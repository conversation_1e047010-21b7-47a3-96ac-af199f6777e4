import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'package:dio/dio.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../models/network_stats.dart';

/// Network monitoring and testing service
class NetworkService {
  static final NetworkService _instance = NetworkService._internal();
  factory NetworkService() => _instance;
  NetworkService._internal();

  final Dio _dio = Dio();
  final Connectivity _connectivity = Connectivity();

  Timer? _monitoringTimer;
  final StreamController<NetworkStats> _statsController = StreamController<NetworkStats>.broadcast();
  final StreamController<bool> _connectivityController = StreamController<bool>.broadcast();

  Stream<NetworkStats> get statsStream => _statsController.stream;
  Stream<bool> get connectivityStream => _connectivityController.stream;

  /// Initialize network monitoring
  Future<void> initialize() async {
    // Monitor connectivity changes
    _connectivity.onConnectivityChanged.listen((ConnectivityResult result) {
      _connectivityController.add(result != ConnectivityResult.none);
    });

    // Start periodic network monitoring
    startMonitoring();
  }

  /// Start continuous network monitoring
  void startMonitoring({Duration interval = const Duration(seconds: 30)}) {
    _monitoringTimer?.cancel();
    _monitoringTimer = Timer.periodic(interval, (_) async {
      try {
        final stats = await measureNetworkPerformance();
        _statsController.add(stats);
      } catch (e) {
        print('Network monitoring error: $e');
      }
    });
  }

  /// Stop network monitoring
  void stopMonitoring() {
    _monitoringTimer?.cancel();
    _monitoringTimer = null;
  }

  /// Measure current network performance
  Future<NetworkStats> measureNetworkPerformance() async {
    try {
      final ping = await measurePing();
      final speeds = await measureSpeed();
      
      return NetworkStats(
        downloadSpeed: speeds['download'] ?? 0.0,
        uploadSpeed: speeds['upload'] ?? 0.0,
        ping: ping,
        jitter: await measureJitter(),
        packetLoss: await measurePacketLoss(),
        timestamp: DateTime.now(),
        serverLocation: 'Test Server',
      );
    } catch (e) {
      print('Network performance measurement error: $e');
      return NetworkStats.empty();
    }
  }

  /// Measure ping to a target host
  Future<int> measurePing({String host = '*******'}) async {
    try {
      final stopwatch = Stopwatch()..start();
      
      // Use HTTP request as ping alternative for web compatibility
      await _dio.get(
        'https://dns.google',
        options: Options(
          connectTimeout: const Duration(seconds: 5),
          receiveTimeout: const Duration(seconds: 5),
        ),
      );
      
      stopwatch.stop();
      return stopwatch.elapsedMilliseconds;
    } catch (e) {
      print('Ping measurement error: $e');
      return 999; // High ping indicates connection issues
    }
  }

  /// Measure network speed (simplified implementation)
  Future<Map<String, double>> measureSpeed() async {
    try {
      // Download speed test using a small file
      final downloadSpeed = await _measureDownloadSpeed();
      
      // Upload speed test (simplified)
      final uploadSpeed = await _measureUploadSpeed();
      
      return {
        'download': downloadSpeed,
        'upload': uploadSpeed,
      };
    } catch (e) {
      print('Speed measurement error: $e');
      return {'download': 0.0, 'upload': 0.0};
    }
  }

  /// Measure download speed
  Future<double> _measureDownloadSpeed() async {
    try {
      const testUrl = 'https://httpbin.org/bytes/1048576'; // 1MB test file
      final stopwatch = Stopwatch()..start();
      
      final response = await _dio.get(
        testUrl,
        options: Options(responseType: ResponseType.bytes),
      );
      
      stopwatch.stop();
      
      if (response.statusCode == 200) {
        final bytes = response.data.length;
        final seconds = stopwatch.elapsedMilliseconds / 1000.0;
        final mbps = (bytes * 8) / (seconds * 1000000); // Convert to Mbps
        return mbps;
      }
      
      return 0.0;
    } catch (e) {
      print('Download speed measurement error: $e');
      return 0.0;
    }
  }

  /// Measure upload speed (simplified)
  Future<double> _measureUploadSpeed() async {
    try {
      // Create test data
      final testData = List.generate(1024 * 100, (index) => index % 256); // 100KB
      final stopwatch = Stopwatch()..start();
      
      await _dio.post(
        'https://httpbin.org/post',
        data: testData,
        options: Options(
          contentType: 'application/octet-stream',
        ),
      );
      
      stopwatch.stop();
      
      final bytes = testData.length;
      final seconds = stopwatch.elapsedMilliseconds / 1000.0;
      final mbps = (bytes * 8) / (seconds * 1000000); // Convert to Mbps
      return mbps;
    } catch (e) {
      print('Upload speed measurement error: $e');
      return 0.0;
    }
  }

  /// Measure network jitter
  Future<int> measureJitter() async {
    try {
      final pings = <int>[];
      
      // Measure multiple pings
      for (int i = 0; i < 5; i++) {
        final ping = await measurePing();
        pings.add(ping);
        await Future.delayed(const Duration(milliseconds: 100));
      }
      
      if (pings.length < 2) return 0;
      
      // Calculate jitter as standard deviation
      final mean = pings.reduce((a, b) => a + b) / pings.length;
      final variance = pings.map((ping) => pow(ping - mean, 2)).reduce((a, b) => a + b) / pings.length;
      return sqrt(variance).round();
    } catch (e) {
      print('Jitter measurement error: $e');
      return 0;
    }
  }

  /// Measure packet loss (simplified)
  Future<double> measurePacketLoss() async {
    try {
      const attempts = 10;
      int successful = 0;
      
      for (int i = 0; i < attempts; i++) {
        try {
          await _dio.get(
            'https://dns.google',
            options: Options(
              connectTimeout: const Duration(seconds: 2),
              receiveTimeout: const Duration(seconds: 2),
            ),
          );
          successful++;
        } catch (e) {
          // Packet lost
        }
        await Future.delayed(const Duration(milliseconds: 100));
      }
      
      return ((attempts - successful) / attempts) * 100;
    } catch (e) {
      print('Packet loss measurement error: $e');
      return 0.0;
    }
  }

  /// Get current network information (simplified)
  Future<Map<String, String?>> getNetworkInfo() async {
    try {
      // Simplified network info without external dependencies
      return {
        'connectionType': (await _connectivity.checkConnectivity()).name,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      print('Network info error: $e');
      return {};
    }
  }

  /// Get current public IP address
  Future<String?> getPublicIP() async {
    try {
      // Try multiple IP detection services for reliability
      final services = [
        'https://api.ipify.org',
        'https://ipinfo.io/ip',
        'https://icanhazip.com',
        'https://httpbin.org/ip',
      ];

      for (final service in services) {
        try {
          final response = await _dio.get(
            service,
            options: Options(
              receiveTimeout: const Duration(seconds: 5),
              sendTimeout: const Duration(seconds: 5),
            ),
          );

          if (response.statusCode == 200) {
            String ip = response.data.toString().trim();

            // Handle JSON response from httpbin
            if (service.contains('httpbin')) {
              final data = response.data as Map<String, dynamic>;
              ip = data['origin'].toString().trim();
            }

            // Validate IP format
            if (_isValidIP(ip)) {
              return ip;
            }
          }
        } catch (e) {
          print('Failed to get IP from $service: $e');
          continue;
        }
      }

      return null;
    } catch (e) {
      print('Failed to get public IP: $e');
      return null;
    }
  }

  /// Validate IP address format
  bool _isValidIP(String ip) {
    final parts = ip.split('.');
    if (parts.length != 4) return false;

    for (final part in parts) {
      final num = int.tryParse(part);
      if (num == null || num < 0 || num > 255) return false;
    }

    return true;
  }

  /// Check if connected to internet
  Future<bool> hasInternetConnection() async {
    try {
      final result = await _connectivity.checkConnectivity();
      if (result == ConnectivityResult.none) return false;

      // Verify actual internet connectivity
      await _dio.get(
        'https://dns.google',
        options: Options(
          connectTimeout: const Duration(seconds: 5),
          receiveTimeout: const Duration(seconds: 5),
        ),
      );
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Dispose resources
  void dispose() {
    _monitoringTimer?.cancel();
    _statsController.close();
    _connectivityController.close();
    _dio.close();
  }
}
