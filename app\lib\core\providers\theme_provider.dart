import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Theme mode provider for managing light/dark theme switching
final themeModeProvider = StateNotifierProvider<ThemeModeNotifier, ThemeMode>((ref) {
  return ThemeModeNotifier();
});

/// Theme mode notifier that persists theme preference
class ThemeModeNotifier extends StateNotifier<ThemeMode> {
  static const String _themeModeKey = 'theme_mode';
  
  ThemeModeNotifier() : super(ThemeMode.dark) {
    _loadThemeMode();
  }

  /// Load theme mode from shared preferences
  Future<void> _loadThemeMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeModeIndex = prefs.getInt(_themeModeKey) ?? ThemeMode.dark.index;
      state = ThemeMode.values[themeModeIndex];
    } catch (e) {
      // Default to dark theme if loading fails
      state = ThemeMode.dark;
    }
  }

  /// Save theme mode to shared preferences
  Future<void> _saveThemeMode(ThemeMode themeMode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_themeModeKey, themeMode.index);
    } catch (e) {
      // Handle error silently
    }
  }

  /// Set theme mode to dark
  Future<void> setDarkMode() async {
    state = ThemeMode.dark;
    await _saveThemeMode(ThemeMode.dark);
  }

  /// Set theme mode to light
  Future<void> setLightMode() async {
    state = ThemeMode.light;
    await _saveThemeMode(ThemeMode.light);
  }

  /// Set theme mode to system
  Future<void> setSystemMode() async {
    state = ThemeMode.system;
    await _saveThemeMode(ThemeMode.system);
  }

  /// Toggle between light and dark mode
  Future<void> toggleTheme() async {
    if (state == ThemeMode.dark) {
      await setLightMode();
    } else {
      await setDarkMode();
    }
  }

  /// Get current theme mode as string
  String get currentThemeName {
    switch (state) {
      case ThemeMode.light:
        return 'Light';
      case ThemeMode.dark:
        return 'Dark';
      case ThemeMode.system:
        return 'System';
    }
  }

  /// Check if current theme is dark
  bool get isDarkMode => state == ThemeMode.dark;

  /// Check if current theme is light
  bool get isLightMode => state == ThemeMode.light;

  /// Check if current theme follows system
  bool get isSystemMode => state == ThemeMode.system;
}

/// Provider for checking if device is in dark mode (system level)
final deviceDarkModeProvider = Provider<bool>((ref) {
  // This would typically be updated by listening to system theme changes
  // For now, we'll default to true (dark mode)
  return true;
});

/// Provider for effective theme mode (considering system theme when in system mode)
final effectiveThemeModeProvider = Provider<ThemeMode>((ref) {
  final themeMode = ref.watch(themeModeProvider);
  final deviceDarkMode = ref.watch(deviceDarkModeProvider);
  
  if (themeMode == ThemeMode.system) {
    return deviceDarkMode ? ThemeMode.dark : ThemeMode.light;
  }
  
  return themeMode;
});

/// Provider for checking if current effective theme is dark
final isDarkThemeProvider = Provider<bool>((ref) {
  final effectiveTheme = ref.watch(effectiveThemeModeProvider);
  return effectiveTheme == ThemeMode.dark;
});
