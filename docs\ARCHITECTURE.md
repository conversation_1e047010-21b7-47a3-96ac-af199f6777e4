# Project Chimera - System Architecture

## Overview

This document provides detailed architectural diagrams and data flow specifications for Project Chimera's multi-hop VPN chain system.

## 1. High-Level System Architecture

```mermaid
graph TB
    subgraph "User Device"
        UI[User Interface]
        CM[Chain Manager]
        CO[Connection Orchestrator]
        SM[Security Manager]
        PM[Protocol Manager]
    end
    
    subgraph "VPN Chain"
        H1[Hop 1<br/>WireGuard]
        H2[Hop 2<br/>OpenVPN]
        H3[Hop 3<br/>WireGuard]
        TOR[Tor Network<br/>Optional]
    end
    
    subgraph "External Services"
        CS[Community Servers]
        US[User Servers]
        DNS[Trusted DNS]
    end
    
    UI --> CM
    CM --> CO
    CO --> SM
    SM --> PM
    PM --> H1
    H1 --> H2
    H2 --> H3
    H3 --> TOR
    TOR --> Internet
    
    CM <--> CS
    CM <--> US
    SM --> DNS
```

## 2. Multi-Hop Connection Data Flow

### 2.1 Connection Establishment Sequence

```mermaid
sequenceDiagram
    participant User
    participant ChainManager
    participant ConnectionOrchestrator
    participant SecurityManager
    participant Hop1
    participant Hop2
    participant Hop3
    participant Internet
    
    User->>ChainManager: Select/Create Chain
    ChainManager->>ConnectionOrchestrator: Validate Chain Config
    ConnectionOrchestrator->>SecurityManager: Enable Kill Switch
    SecurityManager-->>ConnectionOrchestrator: Kill Switch Active
    
    ConnectionOrchestrator->>Hop1: Establish Connection
    Hop1-->>ConnectionOrchestrator: Connection Established
    
    ConnectionOrchestrator->>Hop2: Connect via Hop1
    Hop2-->>ConnectionOrchestrator: Connection Established
    
    ConnectionOrchestrator->>Hop3: Connect via Hop1->Hop2
    Hop3-->>ConnectionOrchestrator: Connection Established
    
    ConnectionOrchestrator->>User: Chain Active
    
    User->>Hop1: Send Traffic
    Hop1->>Hop2: Forward Traffic
    Hop2->>Hop3: Forward Traffic
    Hop3->>Internet: Forward Traffic
    
    Internet-->>Hop3: Response
    Hop3-->>Hop2: Response
    Hop2-->>Hop1: Response
    Hop1-->>User: Response
```

### 2.2 Traffic Routing Architecture

```mermaid
graph LR
    subgraph "User Device"
        APP[Applications]
        ST[Split Tunneling]
        KS[Kill Switch]
        DNS[DNS Protection]
        IPV6[IPv6 Protection]
    end
    
    subgraph "VPN Chain Processing"
        OBF[Obfuscation Layer]
        ENC1[Encryption Layer 1]
        ENC2[Encryption Layer 2]
        ENC3[Encryption Layer 3]
    end
    
    subgraph "Network Hops"
        H1[Hop 1]
        H2[Hop 2]
        H3[Hop 3]
    end
    
    APP --> ST
    ST --> KS
    KS --> DNS
    DNS --> IPV6
    IPV6 --> OBF
    OBF --> ENC1
    ENC1 --> H1
    H1 --> ENC2
    ENC2 --> H2
    H2 --> ENC3
    ENC3 --> H3
    H3 --> Internet
```

## 3. Component Architecture

### 3.1 Core Components

```mermaid
classDiagram
    class ChainManager {
        +createChain(config)
        +saveChain(chain)
        +loadChain(id)
        +validateChain(chain)
        +getAvailableServers()
    }
    
    class ConnectionOrchestrator {
        +establishChain(chain)
        +disconnectChain()
        +monitorHealth()
        +handleFailure(hop)
    }
    
    class SecurityManager {
        +enableKillSwitch()
        +configureDNS(providers)
        +blockIPv6()
        +applySplitTunneling(rules)
    }
    
    class ProtocolManager {
        +createConnection(protocol, config)
        +getProtocolHandler(type)
        +supportedProtocols()
    }
    
    class ServerManager {
        +addUserServer(config)
        +importCommunityServers()
        +validateServer(server)
        +monitorPerformance(server)
    }
    
    ChainManager --> ConnectionOrchestrator
    ConnectionOrchestrator --> SecurityManager
    ConnectionOrchestrator --> ProtocolManager
    ChainManager --> ServerManager
```

### 3.2 Protocol Handler Architecture

```mermaid
classDiagram
    class VPNProtocol {
        <<interface>>
        +connect(config)
        +disconnect()
        +getStatus()
        +getStatistics()
        +supportsObfuscation()
    }
    
    class WireGuardProtocol {
        +connect(config)
        +disconnect()
        +getStatus()
        +getStatistics()
        +supportsObfuscation()
        -generateKeyPair()
        -configureInterface()
    }
    
    class OpenVPNProtocol {
        +connect(config)
        +disconnect()
        +getStatus()
        +getStatistics()
        +supportsObfuscation()
        -parseCertificates()
        -configureRouting()
    }
    
    class ObfuscationHandler {
        +enableObfs4(config)
        +enableStunnel(config)
        +disable()
        +getStatus()
    }
    
    VPNProtocol <|-- WireGuardProtocol
    VPNProtocol <|-- OpenVPNProtocol
    WireGuardProtocol --> ObfuscationHandler
    OpenVPNProtocol --> ObfuscationHandler
```

## 4. Security Architecture

### 4.1 Security Layer Stack

```mermaid
graph TB
    subgraph "Application Layer"
        UI[User Interface]
        BL[Business Logic]
    end
    
    subgraph "Security Enforcement Layer"
        KS[Kill Switch Monitor]
        DNS[DNS Leak Protection]
        IPV6[IPv6 Leak Protection]
        ST[Split Tunneling]
    end
    
    subgraph "Protocol Security Layer"
        ENC[End-to-End Encryption]
        AUTH[Authentication]
        INT[Integrity Checking]
    end
    
    subgraph "Network Layer"
        OBF[Traffic Obfuscation]
        ROUTE[Secure Routing]
        MON[Connection Monitoring]
    end
    
    subgraph "System Layer"
        FW[Firewall Rules]
        NET[Network Interface Control]
        PROC[Process Isolation]
    end
    
    UI --> BL
    BL --> KS
    KS --> DNS
    DNS --> IPV6
    IPV6 --> ST
    ST --> ENC
    ENC --> AUTH
    AUTH --> INT
    INT --> OBF
    OBF --> ROUTE
    ROUTE --> MON
    MON --> FW
    FW --> NET
    NET --> PROC
```

### 4.2 Kill Switch Implementation

```mermaid
stateDiagram-v2
    [*] --> Disabled
    Disabled --> Enabling : User Activates
    Enabling --> Active : Rules Applied
    Active --> Monitoring : VPN Connected
    Monitoring --> Blocking : Connection Lost
    Blocking --> Monitoring : Connection Restored
    Monitoring --> Active : User Disconnects VPN
    Active --> Disabled : User Deactivates
    Blocking --> Disabled : Emergency Disable
    
    note right of Blocking
        All traffic blocked
        except VPN reconnection
        attempts
    end note
```

## 5. Data Storage Architecture

### 5.1 Configuration Storage

```mermaid
erDiagram
    CHAIN {
        string chainId PK
        string name
        string description
        json hops
        boolean torExit
        timestamp created
        timestamp lastUsed
        int totalConnections
    }
    
    SERVER {
        string serverId PK
        string name
        string endpoint
        json location
        string source
        string trustLevel
        json protocols
        json performance
        json security
        json reputation
    }
    
    HOP {
        string hopId PK
        string chainId FK
        int order
        string serverId FK
        json protocolConfig
        json obfuscationConfig
    }
    
    USER_SETTINGS {
        string userId PK
        json killSwitchConfig
        json dnsConfig
        json splitTunnelingRules
        json uiPreferences
    }
    
    CHAIN ||--o{ HOP : contains
    SERVER ||--o{ HOP : uses
    USER_SETTINGS ||--o{ CHAIN : owns
```

### 5.2 Runtime State Management

```mermaid
graph TB
    subgraph "Application State"
        AS[App State]
        CS[Connection State]
        SS[Security State]
        US[UI State]
    end
    
    subgraph "Persistent Storage"
        DB[(Local Database)]
        CF[Config Files]
        KS[Keystore/Keychain]
    end
    
    subgraph "Memory Cache"
        SC[Server Cache]
        PC[Performance Cache]
        CC[Connection Cache]
    end
    
    AS --> CS
    AS --> SS
    AS --> US
    
    CS <--> DB
    SS <--> CF
    SS <--> KS
    
    CS <--> SC
    CS <--> PC
    CS <--> CC
```

## 6. Platform-Specific Architecture Considerations

### 6.1 Mobile Platforms (iOS/Android)

```mermaid
graph TB
    subgraph "Flutter App"
        UI[Flutter UI]
        BL[Dart Business Logic]
    end
    
    subgraph "Native Bridge"
        MC[Method Channel]
        EC[Event Channel]
    end
    
    subgraph "Native VPN Core"
        VPN[VPN Service]
        SEC[Security Manager]
        NET[Network Manager]
    end
    
    subgraph "System APIs"
        NEVPN[NEVPNManager iOS]
        VPNSVC[VpnService Android]
        FW[Firewall APIs]
    end
    
    UI <--> BL
    BL <--> MC
    MC <--> VPN
    VPN <--> SEC
    VPN <--> NET
    SEC <--> FW
    NET <--> NEVPN
    NET <--> VPNSVC
```

### 6.2 Desktop Platforms (Windows/macOS/Linux)

```mermaid
graph TB
    subgraph "Flutter Desktop App"
        UI[Flutter UI]
        BL[Dart Business Logic]
    end
    
    subgraph "Native Service"
        SVC[Background Service]
        IPC[IPC Communication]
    end
    
    subgraph "VPN Implementation"
        TUN[TUN/TAP Interface]
        ROUTE[Routing Manager]
        FW[Firewall Manager]
    end
    
    subgraph "System Integration"
        WINTUN[WinTun Windows]
        PFCTL[pfctl macOS]
        IPTABLES[iptables Linux]
    end
    
    UI <--> BL
    BL <--> IPC
    IPC <--> SVC
    SVC <--> TUN
    SVC <--> ROUTE
    SVC <--> FW
    FW <--> WINTUN
    FW <--> PFCTL
    FW <--> IPTABLES
```

This architecture provides a robust foundation for implementing the multi-hop VPN system with comprehensive security features and cross-platform compatibility.
