# Android Implementation Guide - Project Chimera

## Overview

This guide details the Android-specific implementation requirements and strategies for Project Chimera's multi-hop VPN functionality.

## 1. Android Architecture

### 1.1 App Structure

```
app/
├── src/main/
│   ├── java/com/chimera/vpn/
│   │   ├── MainActivity.kt (Flutter Activity)
│   │   ├── VpnService.kt (Core VPN Service)
│   │   ├── ChainManager.kt (Multi-hop Logic)
│   │   ├── ProtocolHandlers/
│   │   │   ├── WireGuardHandler.kt
│   │   │   └── OpenVpnHandler.kt
│   │   ├── Security/
│   │   │   ├── KillSwitch.kt
│   │   │   └── DnsProtection.kt
│   │   └── Bridge/
│   │       └── MethodChannelHandler.kt
│   ├── AndroidManifest.xml
│   └── res/
└── build.gradle
```

### 1.2 Required Permissions

```xml
<!-- AndroidManifest.xml -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
<uses-permission android:name="android.permission.BIND_VPN_SERVICE" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />

<!-- For kill switch functionality -->
<uses-permission android:name="android.permission.CONTROL_VPN" />

<!-- For camera QR code scanning -->
<uses-permission android:name="android.permission.CAMERA" />
```

## 2. VPN Service Implementation

### 2.1 Core VPN Service

```kotlin
import android.net.VpnService
import android.content.Intent
import android.os.ParcelFileDescriptor
import kotlinx.coroutines.*

class ChimeraVpnService : VpnService() {
    private var vpnInterface: ParcelFileDescriptor? = null
    private var chainManager: VPNChainManager? = null
    private var serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    companion object {
        const val ACTION_CONNECT = "com.chimera.vpn.CONNECT"
        const val ACTION_DISCONNECT = "com.chimera.vpn.DISCONNECT"
        const val EXTRA_CHAIN_CONFIG = "chain_config"
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_CONNECT -> {
                val chainConfig = intent.getStringExtra(EXTRA_CHAIN_CONFIG)
                if (chainConfig != null) {
                    serviceScope.launch {
                        connectChain(chainConfig)
                    }
                }
            }
            ACTION_DISCONNECT -> {
                serviceScope.launch {
                    disconnectChain()
                }
            }
        }
        
        return START_STICKY
    }
    
    private suspend fun connectChain(configJson: String) {
        try {
            // Parse chain configuration
            val chainConfig = parseChainConfiguration(configJson)
            
            // Create VPN interface
            vpnInterface = createVpnInterface(chainConfig)
            
            // Initialize chain manager
            chainManager = VPNChainManager(this, vpnInterface!!)
            
            // Establish multi-hop chain
            chainManager?.establishChain(chainConfig)
            
            // Start packet processing
            startPacketProcessing()
            
            // Update notification
            updateNotification("Connected via ${chainConfig.name}")
            
        } catch (e: Exception) {
            handleConnectionError(e)
        }
    }
    
    private fun createVpnInterface(config: VPNChainConfiguration): ParcelFileDescriptor {
        val builder = Builder()
            .setSession(config.name)
            .setMtu(1500)
            .addAddress("********", 32)
            .addRoute("0.0.0.0", 0)
            .addDnsServer("*******")
            .setBlocking(true)
        
        // Configure kill switch
        if (config.killSwitchEnabled) {
            configureKillSwitch(builder)
        }
        
        return builder.establish() ?: throw VPNException("Failed to establish VPN interface")
    }
    
    private fun configureKillSwitch(builder: Builder) {
        // Block all apps except VPN
        try {
            val packageManager = packageManager
            val installedApps = packageManager.getInstalledApplications(0)
            
            for (app in installedApps) {
                if (app.packageName != packageName) {
                    builder.addDisallowedApplication(app.packageName)
                }
            }
        } catch (e: Exception) {
            // Fallback: use PROTECT_FROM_VPN for specific apps
            builder.addAllowedApplication(packageName)
        }
    }
    
    private suspend fun startPacketProcessing() {
        val inputStream = vpnInterface?.fileDescriptor?.let { 
            ParcelFileDescriptor.AutoCloseInputStream(vpnInterface) 
        }
        val outputStream = vpnInterface?.fileDescriptor?.let { 
            ParcelFileDescriptor.AutoCloseOutputStream(vpnInterface) 
        }
        
        serviceScope.launch {
            processPackets(inputStream, outputStream)
        }
    }
    
    private suspend fun processPackets(
        inputStream: ParcelFileDescriptor.AutoCloseInputStream?,
        outputStream: ParcelFileDescriptor.AutoCloseOutputStream?
    ) {
        val buffer = ByteArray(32767)
        
        while (isActive && inputStream != null && outputStream != null) {
            try {
                val length = inputStream.read(buffer)
                if (length > 0) {
                    // Route packet through VPN chain
                    val processedPacket = chainManager?.routePacket(buffer, length)
                    
                    if (processedPacket != null) {
                        outputStream.write(processedPacket)
                    }
                }
            } catch (e: Exception) {
                if (isActive) {
                    handlePacketError(e)
                }
                break
            }
        }
    }
}
```

### 2.2 VPN Chain Manager

```kotlin
class VPNChainManager(
    private val context: Context,
    private val vpnInterface: ParcelFileDescriptor
) {
    private val hops = mutableListOf<VPNHop>()
    private var isConnected = false
    
    suspend fun establishChain(config: VPNChainConfiguration) {
        // Validate configuration
        validateChainConfiguration(config)
        
        // Establish hops sequentially
        for ((index, hopConfig) in config.hops.withIndex()) {
            val hop = establishHop(hopConfig, index)
            hops.add(hop)
        }
        
        // Configure routing
        configureChainRouting()
        
        isConnected = true
    }
    
    private suspend fun establishHop(config: VPNHopConfiguration, index: Int): VPNHop {
        val protocolHandler = createProtocolHandler(config.protocol)
        
        return if (index == 0) {
            // First hop connects directly
            protocolHandler.connect(config.server)
        } else {
            // Subsequent hops route through previous hops
            val routingPath = hops.subList(0, index)
            protocolHandler.connectThroughPath(config.server, routingPath)
        }
    }
    
    private fun createProtocolHandler(protocol: VPNProtocolType): VPNProtocolHandler {
        return when (protocol) {
            VPNProtocolType.WIREGUARD -> WireGuardHandler(context)
            VPNProtocolType.OPENVPN -> OpenVPNHandler(context)
        }
    }
    
    suspend fun routePacket(packet: ByteArray, length: Int): ByteArray? {
        if (!isConnected || hops.isEmpty()) {
            return null
        }
        
        // Route through chain
        var currentPacket = packet.copyOf(length)
        
        for (hop in hops) {
            currentPacket = hop.processPacket(currentPacket) ?: return null
        }
        
        return currentPacket
    }
}
```

## 3. Protocol Implementations

### 3.1 WireGuard Handler

```kotlin
import com.wireguard.android.backend.GoBackend
import com.wireguard.config.Config

class WireGuardHandler(private val context: Context) : VPNProtocolHandler {
    private var backend: GoBackend? = null
    private var tunnel: Tunnel? = null
    
    override suspend fun connect(server: VPNServer): VPNHop {
        // Initialize WireGuard backend
        backend = GoBackend(context)
        
        // Parse WireGuard configuration
        val config = Config.parse(server.configuration.inputStream())
        
        // Create tunnel
        tunnel = Tunnel(server.name, config, Tunnel.State.DOWN)
        
        // Start tunnel
        backend?.setState(tunnel!!, Tunnel.State.UP, null)
        
        return VPNHop(server, VPNProtocolType.WIREGUARD, tunnel)
    }
    
    override suspend fun connectThroughPath(
        server: VPNServer, 
        path: List<VPNHop>
    ): VPNHop {
        // Configure routing through existing hops
        val routedConfig = configureRoutingThroughPath(server.configuration, path)
        
        // Create server with routed configuration
        val routedServer = server.copy(configuration = routedConfig)
        
        return connect(routedServer)
    }
    
    private fun configureRoutingThroughPath(
        config: String, 
        path: List<VPNHop>
    ): String {
        // Modify WireGuard config to route through existing hops
        val configLines = config.lines().toMutableList()
        
        // Find [Peer] section and modify endpoint
        val peerIndex = configLines.indexOfFirst { it.startsWith("[Peer]") }
        if (peerIndex != -1) {
            // Route through last hop in path
            val lastHop = path.lastOrNull()
            if (lastHop != null) {
                val endpointIndex = configLines.indexOfFirst { 
                    it.startsWith("Endpoint") 
                }
                if (endpointIndex != -1) {
                    configLines[endpointIndex] = "Endpoint = ${lastHop.getProxyEndpoint()}"
                }
            }
        }
        
        return configLines.joinToString("\n")
    }
}
```

### 3.2 OpenVPN Handler

```kotlin
import de.blinkt.openvpn.core.VpnStatus
import de.blinkt.openvpn.core.ConfigParser
import de.blinkt.openvpn.core.VPNLaunchHelper

class OpenVPNHandler(private val context: Context) : VPNProtocolHandler {
    private var vpnProfile: VpnProfile? = null
    
    override suspend fun connect(server: VPNServer): VPNHop {
        // Parse OpenVPN configuration
        val configParser = ConfigParser()
        configParser.parseConfig(server.configuration.reader())
        vpnProfile = configParser.convertProfile()
        
        // Configure profile
        vpnProfile?.apply {
            mName = server.name
            mUsername = server.credentials?.username
            mPassword = server.credentials?.password
        }
        
        // Start OpenVPN connection
        VPNLaunchHelper.startOpenVpn(vpnProfile!!, context)
        
        // Wait for connection
        waitForConnection()
        
        return VPNHop(server, VPNProtocolType.OPENVPN, vpnProfile)
    }
    
    override suspend fun connectThroughPath(
        server: VPNServer, 
        path: List<VPNHop>
    ): VPNHop {
        // Configure SOCKS proxy through existing hops
        val proxyConfig = configureProxyThroughPath(server.configuration, path)
        
        val proxiedServer = server.copy(configuration = proxyConfig)
        return connect(proxiedServer)
    }
    
    private suspend fun waitForConnection() {
        return suspendCancellableCoroutine { continuation ->
            val statusListener = object : VpnStatus.StateListener {
                override fun updateState(
                    state: String?, 
                    logmessage: String?, 
                    localizedResId: Int, 
                    level: VpnStatus.ConnectionStatus?
                ) {
                    when (level) {
                        VpnStatus.ConnectionStatus.LEVEL_CONNECTED -> {
                            VpnStatus.removeStateListener(this)
                            continuation.resume(Unit)
                        }
                        VpnStatus.ConnectionStatus.LEVEL_NOTCONNECTED -> {
                            VpnStatus.removeStateListener(this)
                            continuation.resumeWithException(
                                VPNException("OpenVPN connection failed")
                            )
                        }
                        else -> {
                            // Continue waiting
                        }
                    }
                }
                
                override fun setConnectedVPN(uuid: String?) {}
            }
            
            VpnStatus.addStateListener(statusListener)
            
            continuation.invokeOnCancellation {
                VpnStatus.removeStateListener(statusListener)
            }
        }
    }
}
```

## 4. Security Implementation

### 4.1 Kill Switch

```kotlin
class AndroidKillSwitch(private val context: Context) {
    private var isEnabled = false
    
    fun enableKillSwitch(builder: VpnService.Builder) {
        if (isEnabled) return
        
        try {
            // Method 1: Block all apps except VPN
            blockAllAppsExceptVPN(builder)
        } catch (e: Exception) {
            // Method 2: Use firewall rules (requires root)
            try {
                enableFirewallKillSwitch()
            } catch (e2: Exception) {
                // Method 3: Monitor and kill apps
                enableAppMonitoringKillSwitch()
            }
        }
        
        isEnabled = true
    }
    
    private fun blockAllAppsExceptVPN(builder: VpnService.Builder) {
        val packageManager = context.packageManager
        val installedApps = packageManager.getInstalledApplications(0)
        
        for (app in installedApps) {
            if (app.packageName != context.packageName) {
                try {
                    builder.addDisallowedApplication(app.packageName)
                } catch (e: Exception) {
                    // Some system apps cannot be blocked
                    Log.w("KillSwitch", "Cannot block ${app.packageName}: ${e.message}")
                }
            }
        }
    }
    
    private fun enableFirewallKillSwitch() {
        // Requires root access
        val commands = arrayOf(
            "iptables -I OUTPUT -j DROP",
            "iptables -I OUTPUT -o tun+ -j ACCEPT",
            "iptables -I OUTPUT -d 127.0.0.1 -j ACCEPT"
        )
        
        for (command in commands) {
            Runtime.getRuntime().exec(arrayOf("su", "-c", command))
        }
    }
    
    private fun enableAppMonitoringKillSwitch() {
        // Monitor network connections and kill apps that bypass VPN
        CoroutineScope(Dispatchers.IO).launch {
            while (isEnabled) {
                monitorNetworkConnections()
                delay(1000)
            }
        }
    }
    
    fun disableKillSwitch() {
        isEnabled = false
        
        // Clean up firewall rules if used
        try {
            val commands = arrayOf(
                "iptables -D OUTPUT -j DROP",
                "iptables -D OUTPUT -o tun+ -j ACCEPT",
                "iptables -D OUTPUT -d 127.0.0.1 -j ACCEPT"
            )
            
            for (command in commands) {
                Runtime.getRuntime().exec(arrayOf("su", "-c", command))
            }
        } catch (e: Exception) {
            // Ignore errors during cleanup
        }
    }
}
```

### 4.2 DNS Protection

```kotlin
class AndroidDNSProtection(private val context: Context) {
    private var dnsServers = listOf("*******", "*******") // Cloudflare
    
    fun configureDNSProtection(builder: VpnService.Builder) {
        // Add trusted DNS servers
        for (server in dnsServers) {
            builder.addDnsServer(server)
        }
        
        // Route all DNS traffic through VPN
        builder.addRoute("*******", 32)
        builder.addRoute("*******", 32)
        builder.addRoute("*******", 32)
        builder.addRoute("***************", 32)
    }
    
    fun setTrustedDNSServers(servers: List<String>) {
        dnsServers = servers
    }
    
    suspend fun performDNSLeakTest(): DNSLeakResult {
        val testDomains = listOf(
            "test1.dnsleaktest.com",
            "test2.dnsleaktest.com",
            "test3.dnsleaktest.com"
        )
        
        val results = mutableListOf<DNSQueryResult>()
        
        for (domain in testDomains) {
            try {
                val addresses = InetAddress.getAllByName(domain)
                val result = DNSQueryResult(
                    domain = domain,
                    resolvedIPs = addresses.map { it.hostAddress },
                    serverIP = getCurrentDNSServer(),
                    timestamp = System.currentTimeMillis()
                )
                results.add(result)
            } catch (e: Exception) {
                Log.e("DNSLeakTest", "Failed to resolve $domain: ${e.message}")
            }
        }
        
        return analyzeDNSResults(results)
    }
    
    private fun getCurrentDNSServer(): String {
        // Get current DNS server from system properties
        return try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) 
                as ConnectivityManager
            val activeNetwork = connectivityManager.activeNetwork
            val linkProperties = connectivityManager.getLinkProperties(activeNetwork)
            linkProperties?.dnsServers?.firstOrNull()?.hostAddress ?: "unknown"
        } catch (e: Exception) {
            "unknown"
        }
    }
}
```

## 5. Flutter Integration

### 5.1 Method Channel Bridge

```kotlin
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel

class ChimeraMethodChannelPlugin : FlutterPlugin, MethodChannel.MethodCallHandler {
    private lateinit var channel: MethodChannel
    private lateinit var context: Context
    
    override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        channel = MethodChannel(flutterPluginBinding.binaryMessenger, "com.chimera.vpn/native")
        channel.setMethodCallHandler(this)
        context = flutterPluginBinding.applicationContext
    }
    
    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        when (call.method) {
            "connectChain" -> handleConnectChain(call, result)
            "disconnectChain" -> handleDisconnectChain(call, result)
            "getConnectionStatus" -> handleGetConnectionStatus(call, result)
            "enableKillSwitch" -> handleEnableKillSwitch(call, result)
            "performDNSLeakTest" -> handleDNSLeakTest(call, result)
            else -> result.notImplemented()
        }
    }
    
    private fun handleConnectChain(call: MethodCall, result: MethodChannel.Result) {
        val chainConfig = call.argument<String>("chainConfig")
        
        if (chainConfig == null) {
            result.error("INVALID_ARGUMENTS", "Chain configuration required", null)
            return
        }
        
        // Check VPN permission
        val intent = VpnService.prepare(context)
        if (intent != null) {
            result.error("VPN_PERMISSION_REQUIRED", "VPN permission not granted", null)
            return
        }
        
        // Start VPN service
        val serviceIntent = Intent(context, ChimeraVpnService::class.java).apply {
            action = ChimeraVpnService.ACTION_CONNECT
            putExtra(ChimeraVpnService.EXTRA_CHAIN_CONFIG, chainConfig)
        }
        
        context.startForegroundService(serviceIntent)
        result.success(true)
    }
    
    private fun handleDNSLeakTest(call: MethodCall, result: MethodChannel.Result) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val dnsProtection = AndroidDNSProtection(context)
                val leakResult = dnsProtection.performDNSLeakTest()
                
                val resultMap = mapOf(
                    "hasLeak" to leakResult.hasLeak,
                    "leakedServers" to leakResult.leakedServers,
                    "vpnServers" to leakResult.vpnServers,
                    "testResults" to leakResult.testResults.map { it.toMap() }
                )
                
                withContext(Dispatchers.Main) {
                    result.success(resultMap)
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    result.error("DNS_LEAK_TEST_FAILED", e.message, null)
                }
            }
        }
    }
}
```

This Android implementation guide provides the foundation for building Project Chimera's multi-hop VPN functionality on Android while ensuring proper security and performance.
